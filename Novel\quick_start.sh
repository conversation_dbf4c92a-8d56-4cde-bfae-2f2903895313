#!/bin/bash

# Novel LLM-SRec 一键快速开始脚本 (Linux服务器版)
# 用法: bash quick_start.sh

set -e  # 遇到错误时退出

echo "=========================================="
echo "Novel LLM-SRec 快速开始 (Linux服务器)"
echo "=========================================="

# 检查Python环境
echo "检查Python环境..."
python3 --version 2>/dev/null || python --version || {
    echo "错误: 未找到Python，请先安装Python 3.8+"
    exit 1
}

# 使用python3或python
PYTHON_CMD="python3"
command -v python3 >/dev/null 2>&1 || PYTHON_CMD="python"

# 检查依赖
echo "检查依赖..."
$PYTHON_CMD -c "import torch; print(f'PyTorch: {torch.__version__}')" 2>/dev/null || {
    echo "正在安装依赖..."
    pip3 install -r requirements.txt 2>/dev/null || pip install -r requirements.txt
}

echo ""
echo "=========================================="
echo "步骤1: 运行快速测试（约3-5分钟）"
echo "=========================================="

# 运行快速测试
$PYTHON_CMD run_experiment.py --config experiments/configs/quick_test.yaml

echo ""
echo "=========================================="
echo "步骤2: 查看测试结果"
echo "=========================================="

# 检查结果文件是否存在
if [ -f "experiments/runs/quick_test/results/latest_results.json" ]; then
    echo "✅ 快速测试完成！"
    echo ""
    echo "实验结果:"
    $PYTHON_CMD -c "
import json
try:
    with open('experiments/runs/quick_test/results/latest_results.json', 'r') as f:
        results = json.load(f)
    eval_results = results.get('evaluation_results', {})
    print('=== 评估指标 ===')
    for metric, value in eval_results.items():
        if isinstance(value, (int, float)):
            print(f'{metric}: {value:.4f}')
    print(f'训练时间: {results.get(\"experiment_time\", 0):.2f}秒')
except Exception as e:
    print(f'读取结果失败: {e}')
"
else
    echo "❌ 快速测试失败，请检查日志文件"
    if [ -f "experiments/runs/quick_test/experiment.log" ]; then
        echo "错误日志:"
        tail -10 experiments/runs/quick_test/experiment.log
    fi
    exit 1
fi

echo ""
echo "=========================================="
echo "步骤3: 准备完整实验数据集"
echo "=========================================="

echo "准备Beauty数据集（中等规模，适合完整实验）..."
$PYTHON_CMD prepare_data.py --dataset Beauty

echo ""
echo "=========================================="
echo "步骤4: 运行完整实验（可选）"
echo "=========================================="

echo "您可以运行以下命令进行完整实验:"
echo ""
echo "# 运行Beauty数据集实验"
echo "$PYTHON_CMD run_experiment.py --dataset Beauty --experiment-name beauty_experiment --num-epochs 10"
echo ""
echo "# 查看实验结果"
echo "$PYTHON_CMD -c \"
import json
with open('experiments/runs/beauty_experiment/results/latest_results.json', 'r') as f:
    results = json.load(f)
eval_results = results.get('evaluation_results', {})
print('=== 实验结果 ===')
for metric, value in eval_results.items():
    if isinstance(value, (int, float)):
        print(f'{metric}: {value:.4f}')
print(f'训练时间: {results.get(\\\"experiment_time\\\", 0):.2f}秒')
\""

echo ""
echo "=========================================="
echo "🎉 快速开始完成！"
echo "=========================================="

echo "接下来您可以："
echo "1. 查看详细文档: README.md"
echo "2. 运行完整实验: $PYTHON_CMD run_experiment.py --dataset Beauty"
echo "3. 查看可用数据集: $PYTHON_CMD prepare_data.py --list"
echo "4. 自定义实验配置: experiments/configs/"
echo "5. 查看GPU使用情况: nvidia-smi"
echo ""
echo "Linux服务器使用提示："
echo "- 使用 nohup 在后台运行长时间实验"
echo "- 使用 screen 或 tmux 管理会话"
echo "- 监控GPU内存使用避免OOM"
echo ""
echo "如需帮助，请查看 README.md 或提交Issue"
