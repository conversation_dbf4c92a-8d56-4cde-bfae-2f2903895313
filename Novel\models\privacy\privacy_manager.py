"""
隐私保护管理器

负责端云协同推荐系统中的隐私保护，包括：
1. 差分隐私噪声添加
2. 联邦学习聚合
3. 安全多方计算
4. 用户表示加密传输
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import hashlib
import hmac
from cryptography.fernet import Fernet
import logging

logger = logging.getLogger(__name__)


class PrivacyManager:
    """
    隐私保护管理器
    
    核心功能：
    1. 差分隐私保护
    2. 联邦学习支持
    3. 安全传输加密
    4. 隐私预算管理
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.epsilon = config.get('epsilon', 1.0)  # 隐私预算
        self.delta = config.get('delta', 1e-5)     # 隐私参数
        self.sensitivity = config.get('sensitivity', 1.0)  # 敏感度
        self.clip_norm = config.get('clip_norm', 1.0)      # 梯度裁剪
        
        # 加密密钥
        self.encryption_key = self._generate_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # 隐私预算追踪
        self.privacy_budget_used = 0.0
        self.max_privacy_budget = config.get('max_privacy_budget', 10.0)
        
        logger.info(f"Initialized PrivacyManager with ε={self.epsilon}, δ={self.delta}")
    
    def add_differential_privacy_noise(self, 
                                     data: torch.Tensor, 
                                     noise_type: str = 'gaussian') -> torch.Tensor:
        """
        添加差分隐私噪声
        
        Args:
            data: 原始数据
            noise_type: 噪声类型 ('gaussian' 或 'laplace')
            
        Returns:
            添加噪声后的数据
        """
        if noise_type == 'gaussian':
            return self._add_gaussian_noise(data)
        elif noise_type == 'laplace':
            return self._add_laplace_noise(data)
        else:
            raise ValueError(f"Unsupported noise type: {noise_type}")
    
    def _add_gaussian_noise(self, data: torch.Tensor) -> torch.Tensor:
        """添加高斯噪声（适用于(ε,δ)-差分隐私）"""
        # 计算噪声标准差
        sigma = np.sqrt(2 * np.log(1.25 / self.delta)) * self.sensitivity / self.epsilon
        
        # 生成高斯噪声
        noise = torch.normal(0, sigma, size=data.shape, device=data.device)
        
        # 更新隐私预算
        self.privacy_budget_used += self.epsilon
        
        return data + noise
    
    def _add_laplace_noise(self, data: torch.Tensor) -> torch.Tensor:
        """添加拉普拉斯噪声（适用于ε-差分隐私）"""
        # 计算噪声尺度
        scale = self.sensitivity / self.epsilon
        
        # 生成拉普拉斯噪声
        noise = torch.tensor(
            np.random.laplace(0, scale, data.shape),
            dtype=data.dtype,
            device=data.device
        )
        
        # 更新隐私预算
        self.privacy_budget_used += self.epsilon
        
        return data + noise
    
    def clip_gradients(self, gradients: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        梯度裁剪（差分隐私训练）
        
        Args:
            gradients: 模型梯度字典
            
        Returns:
            裁剪后的梯度
        """
        clipped_gradients = {}
        
        for name, grad in gradients.items():
            if grad is not None:
                # 计算梯度范数
                grad_norm = torch.norm(grad)
                
                # 裁剪梯度
                if grad_norm > self.clip_norm:
                    clipped_gradients[name] = grad * (self.clip_norm / grad_norm)
                else:
                    clipped_gradients[name] = grad
            else:
                clipped_gradients[name] = grad
        
        return clipped_gradients
    
    def federated_averaging(self, 
                          client_models: List[Dict[str, torch.Tensor]], 
                          client_weights: Optional[List[float]] = None) -> Dict[str, torch.Tensor]:
        """
        联邦平均聚合
        
        Args:
            client_models: 客户端模型参数列表
            client_weights: 客户端权重（默认均等权重）
            
        Returns:
            聚合后的全局模型参数
        """
        if client_weights is None:
            client_weights = [1.0 / len(client_models)] * len(client_models)
        
        # 初始化全局模型
        global_model = {}
        
        # 加权平均
        for name in client_models[0].keys():
            global_model[name] = torch.zeros_like(client_models[0][name])
            
            for client_model, weight in zip(client_models, client_weights):
                global_model[name] += weight * client_model[name]
        
        return global_model
    
    def secure_aggregation(self, 
                         client_updates: List[torch.Tensor],
                         num_clients: int) -> torch.Tensor:
        """
        安全聚合（简化版安全多方计算）
        
        Args:
            client_updates: 客户端更新列表
            num_clients: 客户端总数
            
        Returns:
            安全聚合结果
        """
        # 添加随机掩码
        masked_updates = []
        for update in client_updates:
            # 生成随机掩码
            mask = torch.randn_like(update) * 0.1
            masked_updates.append(update + mask)
        
        # 聚合
        aggregated = torch.stack(masked_updates).mean(dim=0)
        
        return aggregated
    
    def encrypt_user_representation(self, user_repr: torch.Tensor) -> bytes:
        """
        加密用户表示用于安全传输
        
        Args:
            user_repr: 用户表示张量
            
        Returns:
            加密后的字节数据
        """
        # 转换为字节
        user_repr_bytes = user_repr.detach().cpu().numpy().tobytes()
        
        # 加密
        encrypted_data = self.cipher_suite.encrypt(user_repr_bytes)
        
        return encrypted_data
    
    def decrypt_user_representation(self, 
                                  encrypted_data: bytes, 
                                  shape: Tuple[int, ...],
                                  dtype: torch.dtype = torch.float32) -> torch.Tensor:
        """
        解密用户表示
        
        Args:
            encrypted_data: 加密数据
            shape: 原始张量形状
            dtype: 数据类型
            
        Returns:
            解密后的用户表示
        """
        # 解密
        decrypted_bytes = self.cipher_suite.decrypt(encrypted_data)
        
        # 转换回张量
        user_repr_array = np.frombuffer(decrypted_bytes, dtype=np.float32)
        user_repr = torch.from_numpy(user_repr_array).reshape(shape).to(dtype)
        
        return user_repr
    
    def generate_user_hash(self, user_id: str, salt: str = None) -> str:
        """
        生成用户哈希ID（匿名化）
        
        Args:
            user_id: 原始用户ID
            salt: 盐值
            
        Returns:
            哈希后的用户ID
        """
        if salt is None:
            salt = self.config.get('hash_salt', 'default_salt')
        
        # 使用HMAC-SHA256
        hash_obj = hmac.new(
            salt.encode('utf-8'),
            user_id.encode('utf-8'),
            hashlib.sha256
        )
        
        return hash_obj.hexdigest()
    
    def check_privacy_budget(self) -> bool:
        """
        检查隐私预算是否充足
        
        Returns:
            是否还有可用的隐私预算
        """
        return self.privacy_budget_used < self.max_privacy_budget
    
    def reset_privacy_budget(self):
        """重置隐私预算"""
        self.privacy_budget_used = 0.0
        logger.info("Privacy budget reset")
    
    def get_privacy_report(self) -> Dict[str, float]:
        """
        获取隐私使用报告
        
        Returns:
            隐私使用统计
        """
        return {
            'epsilon_used': self.privacy_budget_used,
            'epsilon_total': self.max_privacy_budget,
            'epsilon_remaining': self.max_privacy_budget - self.privacy_budget_used,
            'delta': self.delta,
            'usage_ratio': self.privacy_budget_used / self.max_privacy_budget
        }
    
    def _generate_encryption_key(self) -> bytes:
        """生成加密密钥"""
        # 在实际应用中，应该使用更安全的密钥管理
        seed = self.config.get('encryption_seed', 'default_seed')
        key_hash = hashlib.sha256(seed.encode()).digest()
        return Fernet.generate_key()


class DifferentialPrivacyTrainer:
    """
    差分隐私训练器
    
    支持DP-SGD训练
    """
    
    def __init__(self, privacy_manager: PrivacyManager):
        self.privacy_manager = privacy_manager
    
    def dp_sgd_step(self, 
                    model: nn.Module, 
                    loss: torch.Tensor, 
                    optimizer: torch.optim.Optimizer) -> None:
        """
        差分隐私SGD步骤
        
        Args:
            model: 模型
            loss: 损失
            optimizer: 优化器
        """
        # 计算梯度
        loss.backward()
        
        # 获取梯度
        gradients = {}
        for name, param in model.named_parameters():
            if param.grad is not None:
                gradients[name] = param.grad.clone()
        
        # 梯度裁剪
        clipped_gradients = self.privacy_manager.clip_gradients(gradients)
        
        # 添加噪声
        for name, param in model.named_parameters():
            if param.grad is not None:
                noisy_grad = self.privacy_manager.add_differential_privacy_noise(
                    clipped_gradients[name]
                )
                param.grad = noisy_grad
        
        # 优化器步骤
        optimizer.step()
        optimizer.zero_grad()


class SecureCommunication:
    """安全通信模块"""
    
    def __init__(self, privacy_manager: PrivacyManager):
        self.privacy_manager = privacy_manager
    
    def secure_send(self, data: torch.Tensor) -> bytes:
        """安全发送数据"""
        return self.privacy_manager.encrypt_user_representation(data)
    
    def secure_receive(self, encrypted_data: bytes, shape: Tuple[int, ...]) -> torch.Tensor:
        """安全接收数据"""
        return self.privacy_manager.decrypt_user_representation(encrypted_data, shape)
