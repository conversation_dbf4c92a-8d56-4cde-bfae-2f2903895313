# LLM-SRec 实验运行示例

本文件夹包含了LLM-SRec实验运行的核心示例和说明文档。

## 📁 文件结构

```
examples/
├── README.md                    # 本文件
├── quick_start.md              # 快速开始指南
└── scripts/                    # 示例脚本
    └── run_all_datasets.sh     # 批量实验脚本
```

## 🚀 快速开始

### 1. 环境准备

确保您已经安装了所有依赖：

```bash
pip install -r requirements.txt
```

### 2. 数据准备

准备一个数据集（以ml-100k为例）：

```bash
# 查看可用数据集
python prepare_data.py --list

# 准备ml-100k数据集（最小数据集）
python prepare_data.py --dataset ml-100k

# 检查数据集状态
python prepare_data.py --status ml-100k
```

### 3. 运行快速测试

运行一个快速测试实验：

```bash
# 使用预定义的快速测试配置
python run_experiment.py --config experiments/configs/quick_test.yaml

# 或者直接指定数据集
python run_experiment.py --dataset ml-100k --experiment-name my_first_test
```

### 4. 运行完整实验

运行一个完整的实验：

```bash
# 使用Beauty数据集（中等规模）
python run_experiment.py --dataset Beauty --experiment-name beauty_experiment

# 使用自定义配置
python run_experiment.py --config experiments/configs/beauty_experiment.yaml
```

## 📊 实验类型

### 基础实验

最简单的实验类型，使用默认配置：

```bash
python run_experiment.py --dataset Beauty
```

### 自定义参数实验

通过命令行参数自定义训练设置：

```bash
python run_experiment.py \
    --dataset Beauty \
    --batch-size 64 \
    --learning-rate 0.001 \
    --num-epochs 20 \
    --experiment-name beauty_custom
```

### 分步骤实验

分别执行数据准备、训练和评估：

```bash
# 1. 仅准备数据
python run_experiment.py --dataset Beauty --prepare-data-only

# 2. 仅训练模型
python run_experiment.py --config experiments/configs/beauty_experiment.yaml --train-only

# 3. 仅评估模型
python run_experiment.py \
    --config experiments/configs/beauty_experiment.yaml \
    --eval-only \
    --model-path checkpoints/beauty_experiment/model_best.pth
```

## 🎯 预定义实验配置

我们提供了多种预定义的实验配置：

| 配置文件 | 描述 | 适用场景 |
|---------|------|----------|
| `quick_test.yaml` | 快速测试配置 | 验证系统功能 |
| `beauty_experiment.yaml` | Beauty数据集优化配置 | 美妆产品推荐 |
| `movielens_experiment.yaml` | MovieLens数据集配置 | 电影推荐 |
| `collaborative_config.yaml` | 默认协同配置 | 通用推荐任务 |

## 📈 实验结果

实验结果会保存在 `experiments/runs/{experiment_name}/` 目录下：

```
experiments/runs/my_experiment/
├── results/
│   ├── latest_results.json      # 最新结果
│   ├── training_history.csv     # 训练历史
│   └── evaluation_metrics.csv   # 评估指标
├── plots/
│   ├── training_curves.png      # 训练曲线
│   └── evaluation_metrics.png   # 评估指标图
├── checkpoints/                 # 模型检查点
└── experiment.log              # 实验日志
```

## 🔧 常用命令

### 信息查询

```bash
# 列出可用数据集
python run_experiment.py --list-datasets

# 获取数据集信息
python run_experiment.py --dataset-info Beauty

# 列出实验配置
python run_experiment.py --list-configs

# 查看实验状态
python run_experiment.py --experiment-status --config experiments/configs/my_experiment.yaml
```

### 数据集管理

```bash
# 准备多个数据集
python prepare_data.py --dataset Beauty ml-100k

# 强制重新下载
python prepare_data.py --dataset Beauty --force-download

# 仅下载不预处理
python prepare_data.py --dataset Beauty --download-only

# 仅预处理
python prepare_data.py --dataset Beauty --preprocess-only
```

### 模型训练

```bash
# 基础训练
python train_model.py --dataset Beauty

# 自定义训练参数
python train_model.py \
    --dataset Beauty \
    --batch-size 64 \
    --learning-rate 0.001 \
    --epochs 30 \
    --device cuda:0

# 从检查点恢复训练
python train_model.py \
    --config experiments/configs/my_experiment.yaml \
    --resume checkpoints/model_epoch_10.pth
```

## 🐛 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用CPU训练
   python run_experiment.py --dataset Beauty --device cpu
   
   # 减少批次大小
   python run_experiment.py --dataset Beauty --batch-size 16
   ```

2. **数据集下载失败**
   ```bash
   # 检查网络连接，重试下载
   python prepare_data.py --dataset Beauty --force-download
   ```

3. **模型训练中断**
   ```bash
   # 从最新检查点恢复
   python train_model.py --config experiments/configs/my_experiment.yaml --resume checkpoints/latest.pth
   ```

## 📚 更多资源

- [快速开始指南](quick_start.md) - 详细的入门教程
- [批量实验脚本](scripts/run_all_datasets.sh) - 运行所有数据集

## 💡 最佳实践

1. **从快速测试开始** - 使用 `quick_test.yaml` 验证环境
2. **选择合适数据集** - 测试用ml-100k，研究用Beauty/Movies_and_TV
3. **监控资源使用** - 注意GPU内存和计算时间
4. **保存实验记录** - 使用有意义的实验名称

## 📞 支持

如果遇到问题，请检查实验日志文件或提交Issue。
