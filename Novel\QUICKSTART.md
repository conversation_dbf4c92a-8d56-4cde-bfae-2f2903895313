# 🚀 QUICKSTART - 端云协同推荐系统

## ⚡ 快速验证（5分钟）

**目标**：快速验证系统功能，使用预训练模型或小数据集

```bash
# 1. 设置CUDA环境（双3090系统，使用conda安装的CUDA）
export CUDA_HOME=$CONDA_PREFIX
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 验证CUDA
nvcc --version
nvidia-smi

# 2. 安装依赖
pip install -r requirements.txt

# 3. 快速功能验证
chmod +x quick_start.sh
./quick_start.sh
```

## 🎯 完整实验（3-4小时，需要GPU）

**目标**：用完整Movies_and_TV数据集训练端云协同推荐系统

### 步骤1：准备数据 (10-15分钟)
```bash
# 推荐：先用小数据集测试（5分钟）
python prepare_data.py --dataset ml-100k

# 如果小数据集成功，再尝试大数据集（10-15分钟）
python prepare_data.py --dataset Movies_and_TV

# 检查数据状态
python prepare_data.py --status ml-100k
```

### 步骤2：快速功能测试 (5分钟)
```python
from model import create_recommendation_system

# 创建推荐系统（使用小配置快速测试）
rec_system = create_recommendation_system(
    device='cuda',  # Linux系统，使用GPU
    quick_test=True  # 快速测试模式
)

# 模拟用户历史：[电影1, 电影2, 电影3, ...]
user_history = [1, 5, 23, 45, 67]

# 🔥 端云协同推荐
recommendations = rec_system.recommend(user_history, top_k=10)
print(f"推荐结果: {recommendations}")
```

### 步骤3：完整训练 (3-4小时，双3090)
```bash
# 运行完整训练实验（Movies_and_TV: ~1.7M交互）
CUDA_VISIBLE_DEVICES=0,1 python run_experiment.py \
    --dataset Movies_and_TV \
    --experiment-name movies_full \
    --epochs 50 \
    --batch-size 1024 \
    --device cuda \
    --enable-distillation \
    --distributed

# 实际预计时间（双3090）：
# - 数据下载+预处理: 15-20分钟
# - 端侧CF-SRec训练: 45分钟
# - 云端LLM训练: 1.5-2小时
# - 协同训练+蒸馏: 1-1.5小时
# - 总计: 3.5-4.5小时
```
### 步骤4：监控训练进度
```bash
# 实时监控训练日志
tail -f checkpoints/movies_full/train.log

# 查看GPU使用情况
watch -n 1 nvidia-smi

# 检查训练进度
python -c "
import json
import os
if os.path.exists('checkpoints/movies_full/metrics.json'):
    with open('checkpoints/movies_full/metrics.json', 'r') as f:
        metrics = json.load(f)
    print(f'当前Epoch: {metrics.get(\"epoch\", 0)}')
    print(f'训练损失: {metrics.get(\"train_loss\", 0):.4f}')
    print(f'验证NDCG: {metrics.get(\"val_ndcg\", 0):.4f}')
"
```

## 📊 预期结果

### 快速验证模式（5分钟）
- **功能验证**: ✅ 系统各模块正常工作
- **推理测试**: ✅ 三种模式都能生成推荐
- **性能预览**: 基本的延迟和质量指标

### 完整训练模式（3-4小时，双3090）
- **端侧推荐**: 快速响应 (<100ms)，NDCG@10: ~0.18
- **云端推荐**: 深度优化 (200-500ms)，NDCG@10: ~0.24
- **协同推荐**: 平衡效果 (150-300ms)，NDCG@10: ~0.26
- **隐私保护**: 64维用户表示安全传输
- **知识蒸馏**: 端侧性能提升15-20%

## 🛠️ 硬件要求

### 最低配置（功能验证）
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 10GB可用空间
- **时间**: 5-10分钟

### 推荐配置（完整训练）
- **GPU**: 2x RTX 3090 (24GB VRAM each)
- **CPU**: 16核心+
- **内存**: 64GB RAM
- **存储**: 100GB SSD
- **时间**: 3-4小时

### 替代方案
```bash
# 使用小数据集快速训练（30分钟）
python run_experiment.py --dataset ml-100k --epochs 20

# 使用单GPU训练（6-8小时）
python run_experiment.py --dataset Movies_and_TV --epochs 30 --batch-size 256

# CPU训练（不推荐，需要12-24小时）
python run_experiment.py --dataset Movies_and_TV --device cpu --epochs 10
```

## 🔍 故障排除

### 常见问题
- **CUDA OOM**: 减少batch_size: `--batch-size 256` → `--batch-size 128`
- **数据下载慢**: 使用代理或手动下载数据集
- **训练中断**: 使用 `--resume checkpoints/movies_full/latest.pth` 恢复训练
- **内存不足**: 关闭其他程序，或使用 `--num-workers 2` 减少数据加载进程

### Linux系统优化
```bash
# 设置GPU性能模式
sudo nvidia-smi -pm 1
sudo nvidia-smi -pl 400  # 设置功率限制

# 监控系统资源
htop  # CPU和内存
iotop # 磁盘I/O
```

## 🎉 完成！

### 快速验证完成后
✅ 系统功能验证 ✅ 基本推荐测试 ✅ 性能预览

### 完整训练完成后
✅ 端侧序列建模 ✅ 云端LLM增强 ✅ 隐私保护传输 ✅ 协同推荐融合 ✅ 知识蒸馏优化

**下一步**:
- 分析训练结果: `python analyze_results.py --experiment movies_full`
- 查看详细文档: `README.md`
- 自定义模型: 修改 `models/` 目录
