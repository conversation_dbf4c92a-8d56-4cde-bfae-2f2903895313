#!/bin/bash

# 批量运行所有数据集实验脚本
# 用法: bash run_all_datasets.sh

set -e  # 遇到错误时退出

echo "=========================================="
echo "LLM-SRec 批量数据集实验"
echo "=========================================="

# 定义数据集列表
DATASETS=("ml-100k" "Beauty" "Movies_and_TV" "ml-1m")

# 定义实验参数
BATCH_SIZE=32
LEARNING_RATE=1e-4
EPOCHS=20

# 创建结果目录
RESULTS_DIR="experiments/batch_results/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RESULTS_DIR"

echo "结果将保存到: $RESULTS_DIR"
echo ""

# 记录开始时间
START_TIME=$(date +%s)

# 遍历所有数据集
for dataset in "${DATASETS[@]}"; do
    echo "=========================================="
    echo "开始处理数据集: $dataset"
    echo "=========================================="
    
    # 记录单个数据集开始时间
    DATASET_START_TIME=$(date +%s)
    
    # 实验名称
    EXPERIMENT_NAME="batch_${dataset}_$(date +%Y%m%d_%H%M%S)"
    
    echo "实验名称: $EXPERIMENT_NAME"
    echo "批次大小: $BATCH_SIZE"
    echo "学习率: $LEARNING_RATE"
    echo "训练轮数: $EPOCHS"
    echo ""
    
    # 准备数据集
    echo "步骤 1/3: 准备数据集..."
    python prepare_data.py --dataset "$dataset" || {
        echo "错误: 数据集 $dataset 准备失败"
        continue
    }
    
    # 训练模型
    echo "步骤 2/3: 训练模型..."
    python run_experiment.py \
        --dataset "$dataset" \
        --experiment-name "$EXPERIMENT_NAME" \
        --batch-size "$BATCH_SIZE" \
        --learning-rate "$LEARNING_RATE" \
        --num-epochs "$EPOCHS" || {
        echo "错误: 数据集 $dataset 训练失败"
        continue
    }
    
    # 复制结果到批量结果目录
    echo "步骤 3/3: 保存结果..."
    cp -r "experiments/runs/$EXPERIMENT_NAME" "$RESULTS_DIR/"
    
    # 计算单个数据集用时
    DATASET_END_TIME=$(date +%s)
    DATASET_DURATION=$((DATASET_END_TIME - DATASET_START_TIME))
    
    echo "数据集 $dataset 完成，用时: ${DATASET_DURATION}秒"
    echo ""
done

# 计算总用时
END_TIME=$(date +%s)
TOTAL_DURATION=$((END_TIME - START_TIME))

echo "=========================================="
echo "批量实验完成！"
echo "总用时: ${TOTAL_DURATION}秒"
echo "结果保存在: $RESULTS_DIR"
echo "=========================================="

# 生成汇总报告
echo "生成汇总报告..."
python examples/scripts/generate_batch_report.py "$RESULTS_DIR"

echo "汇总报告已生成: $RESULTS_DIR/batch_report.html"
