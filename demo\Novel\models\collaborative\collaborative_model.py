"""
LLM-SRec纯正大小模型协同推荐系统

实现大小模型协同推荐系统：
- 小模型：CF-SRec轻量级序列推荐模型
- 大模型：LLM推荐性能优化模型  
- 协同机制：知识蒸馏 + 特征对齐 + 联合训练
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model, PeftModel

logger = logging.getLogger(__name__)


class CollaborativeRecommendationModel(nn.Module):
    """
    纯正大小模型协同推荐系统主模型
    
    架构特点：
    1. 小模型：轻量级CF-SRec模型处理用户交互序列
    2. 大模型：LLM模型进行推荐性能优化
    3. 协同机制：知识蒸馏和特征对齐
    4. 联合训练：端到端优化
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = config.get('device', 'cuda:0')
        
        # 初始化小模型组件
        self._init_small_model()
        
        # 初始化大模型组件
        self._init_large_model()
        
        # 初始化协同机制
        self._init_collaboration_components()
        
        # 初始化损失函数
        self._init_loss_functions()
        
        logger.info("CollaborativeRecommendationModel initialized successfully")
    
    def _init_small_model(self):
        """初始化小模型组件（CF-SRec）"""
        small_config = self.config['small_model']
        
        # CF-SRec小模型参数
        self.item_num = small_config.get('item_num', 10000)
        self.hidden_units = small_config.get('hidden_units', 64)
        self.num_blocks = small_config.get('num_blocks', 2)
        self.num_heads = small_config.get('num_heads', 1)
        self.dropout_rate = small_config.get('dropout_rate', 0.2)
        self.maxlen = small_config.get('max_sequence_length', 128)
        
        # 物品嵌入层
        self.item_emb = nn.Embedding(self.item_num + 1, self.hidden_units, padding_idx=0)
        self.pos_emb = nn.Embedding(self.maxlen, self.hidden_units)
        
        # 多头自注意力层
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(
                embed_dim=self.hidden_units,
                num_heads=self.num_heads,
                dropout=self.dropout_rate,
                batch_first=True
            ) for _ in range(self.num_blocks)
        ])
        
        # 前馈网络
        self.feed_forward_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.hidden_units, self.hidden_units * 4),
                nn.GELU(),
                nn.Dropout(self.dropout_rate),
                nn.Linear(self.hidden_units * 4, self.hidden_units),
                nn.Dropout(self.dropout_rate)
            ) for _ in range(self.num_blocks)
        ])
        
        # 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(self.hidden_units, eps=1e-8) for _ in range(self.num_blocks * 2)
        ])
        
        self.last_layernorm = nn.LayerNorm(self.hidden_units, eps=1e-8)
        
        # 小模型输出层
        self.small_output = nn.Linear(self.hidden_units, self.item_num)
        
        logger.info(f"Small CF-SRec model initialized with {self.hidden_units} hidden units")
    
    def _init_large_model(self):
        """初始化大模型组件（LLM）"""
        large_config = self.config['large_model']
        
        # LLM模型配置
        self.llm_model_name = large_config.get('llm_model', 'llama-3b')
        self.load_in_8bit = large_config.get('load_in_8bit', True)
        
        # 这里暂时用占位符，实际使用时需要加载真实的LLM模型
        self.llm_hidden_size = 2048  # LLaMA-3B的隐藏层大小
        
        # 推荐性能优化模块
        self.recommendation_optimizer = nn.Sequential(
            nn.Linear(self.hidden_units, self.llm_hidden_size),
            nn.LayerNorm(self.llm_hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size)
        )
        
        # 大模型输出层
        self.large_output = nn.Sequential(
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size // 2, self.item_num)
        )
        
        logger.info(f"Large LLM model components initialized")
    
    def _init_collaboration_components(self):
        """初始化协同机制组件"""
        collab_config = self.config['collaboration']
        
        # 知识蒸馏组件
        self.distillation_temperature = collab_config.get('distillation_temperature', 4.0)
        
        # 特征对齐组件
        self.feature_alignment = nn.Sequential(
            nn.Linear(self.hidden_units, self.llm_hidden_size // 2),
            nn.ReLU(),
            nn.Linear(self.llm_hidden_size // 2, self.llm_hidden_size)
        )
        
        # 用户表示投影层（mu生成）
        self.user_representation_proj = nn.Sequential(
            nn.Linear(self.hidden_units, self.hidden_units * 2),
            nn.LayerNorm(self.hidden_units * 2),
            nn.GELU(),
            nn.Linear(self.hidden_units * 2, self.hidden_units)
        )
        
        # 协同融合层
        self.collaborative_fusion = nn.Sequential(
            nn.Linear(self.hidden_units + self.llm_hidden_size, self.llm_hidden_size),
            nn.LayerNorm(self.llm_hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size, self.item_num)
        )
        
        logger.info("Collaboration components initialized")
    
    def _init_loss_functions(self):
        """初始化损失函数"""
        # 推荐损失
        self.recommendation_loss = nn.CrossEntropyLoss(ignore_index=0)
        
        # 知识蒸馏损失
        self.distillation_loss = nn.KLDivLoss(reduction='batchmean')
        
        # 特征对齐损失
        self.alignment_loss = nn.MSELoss()
        
        logger.info("Loss functions initialized")
    
    def small_model_forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        小模型前向传播
        
        Args:
            input_ids: 输入序列 [batch_size, seq_len]
            attention_mask: 注意力掩码
            
        Returns:
            outputs: 小模型输出字典
        """
        batch_size, seq_len = input_ids.shape
        
        # 位置编码
        positions = torch.arange(seq_len, device=input_ids.device).unsqueeze(0).expand(batch_size, -1)
        
        # 嵌入层
        item_embeddings = self.item_emb(input_ids)  # [batch_size, seq_len, hidden_units]
        pos_embeddings = self.pos_emb(positions)
        
        # 输入表示
        hidden_states = item_embeddings + pos_embeddings
        hidden_states = F.dropout(hidden_states, p=self.dropout_rate, training=self.training)
        
        # 注意力掩码处理
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()
        
        # 因果掩码（下三角矩阵）
        causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=input_ids.device))
        causal_mask = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 多层Transformer
        for i in range(self.num_blocks):
            # 自注意力
            residual = hidden_states
            hidden_states = self.layer_norms[i * 2](hidden_states)
            
            attn_output, _ = self.attention_layers[i](
                hidden_states, hidden_states, hidden_states,
                attn_mask=causal_mask,
                key_padding_mask=(attention_mask == 0)
            )
            hidden_states = residual + attn_output
            
            # 前馈网络
            residual = hidden_states
            hidden_states = self.layer_norms[i * 2 + 1](hidden_states)
            ff_output = self.feed_forward_layers[i](hidden_states)
            hidden_states = residual + ff_output
        
        # 最终层归一化
        hidden_states = self.last_layernorm(hidden_states)
        
        # 用户表示（取最后一个有效位置）
        last_positions = attention_mask.sum(dim=1) - 1  # [batch_size]
        user_representation = hidden_states[torch.arange(batch_size), last_positions]  # [batch_size, hidden_units]
        
        # 生成用户表示mu
        user_mu = self.user_representation_proj(user_representation)
        
        # 小模型预测
        small_logits = self.small_output(user_representation)
        
        return {
            'hidden_states': hidden_states,
            'user_representation': user_representation,
            'user_mu': user_mu,
            'small_logits': small_logits,
            'attention_mask': attention_mask
        }
    
    def large_model_forward(self, user_mu: torch.Tensor, context_info: Dict[str, torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        大模型前向传播
        
        Args:
            user_mu: 用户表示 [batch_size, hidden_units]
            context_info: 上下文信息
            
        Returns:
            outputs: 大模型输出字典
        """
        # 推荐性能优化
        optimized_representation = self.recommendation_optimizer(user_mu)  # [batch_size, llm_hidden_size]
        
        # 大模型预测
        large_logits = self.large_output(optimized_representation)
        
        return {
            'optimized_representation': optimized_representation,
            'large_logits': large_logits
        }
    
    def collaborative_forward(self, small_outputs: Dict[str, torch.Tensor], 
                            large_outputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        协同前向传播
        
        Args:
            small_outputs: 小模型输出
            large_outputs: 大模型输出
            
        Returns:
            outputs: 协同输出字典
        """
        # 特征对齐
        aligned_features = self.feature_alignment(small_outputs['user_mu'])
        
        # 协同融合
        fused_features = torch.cat([
            small_outputs['user_mu'], 
            large_outputs['optimized_representation']
        ], dim=-1)
        
        collaborative_logits = self.collaborative_fusion(fused_features)
        
        return {
            'aligned_features': aligned_features,
            'collaborative_logits': collaborative_logits
        }
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None, 
                targets: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        完整前向传播
        
        Args:
            input_ids: 输入序列
            attention_mask: 注意力掩码
            targets: 目标标签（训练时使用）
            
        Returns:
            outputs: 完整输出字典
        """
        # 小模型前向传播
        small_outputs = self.small_model_forward(input_ids, attention_mask)
        
        # 大模型前向传播
        large_outputs = self.large_model_forward(small_outputs['user_mu'])
        
        # 协同前向传播
        collaborative_outputs = self.collaborative_forward(small_outputs, large_outputs)
        
        # 整合所有输出
        outputs = {
            **small_outputs,
            **large_outputs,
            **collaborative_outputs
        }
        
        # 计算损失（如果提供了目标）
        if targets is not None:
            losses = self.compute_losses(outputs, targets)
            outputs.update(losses)
        
        return outputs
    
    def compute_losses(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算各种损失
        
        Args:
            outputs: 模型输出
            targets: 目标标签
            
        Returns:
            losses: 损失字典
        """
        # 推荐损失
        small_rec_loss = self.recommendation_loss(outputs['small_logits'], targets)
        large_rec_loss = self.recommendation_loss(outputs['large_logits'], targets)
        collaborative_rec_loss = self.recommendation_loss(outputs['collaborative_logits'], targets)
        
        # 知识蒸馏损失（大模型教小模型）
        small_probs = F.log_softmax(outputs['small_logits'] / self.distillation_temperature, dim=-1)
        large_probs = F.softmax(outputs['large_logits'] / self.distillation_temperature, dim=-1)
        distillation_loss = self.distillation_loss(small_probs, large_probs) * (self.distillation_temperature ** 2)
        
        # 特征对齐损失
        alignment_loss = self.alignment_loss(
            outputs['aligned_features'], 
            outputs['optimized_representation']
        )
        
        # 总损失
        total_loss = (
            collaborative_rec_loss + 
            0.3 * small_rec_loss + 
            0.3 * large_rec_loss +
            0.2 * distillation_loss + 
            0.1 * alignment_loss
        )
        
        return {
            'total_loss': total_loss,
            'small_rec_loss': small_rec_loss,
            'large_rec_loss': large_rec_loss,
            'collaborative_rec_loss': collaborative_rec_loss,
            'distillation_loss': distillation_loss,
            'alignment_loss': alignment_loss
        }
    
    def generate_recommendations(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None, 
                               top_k: int = 10) -> torch.Tensor:
        """
        生成推荐结果
        
        Args:
            input_ids: 输入序列
            attention_mask: 注意力掩码
            top_k: 返回top-k推荐
            
        Returns:
            recommendations: 推荐物品ID [batch_size, top_k]
        """
        self.eval()
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask)
            logits = outputs['collaborative_logits']
            
            # 获取top-k推荐
            _, recommendations = torch.topk(logits, k=top_k, dim=-1)
            
        return recommendations
