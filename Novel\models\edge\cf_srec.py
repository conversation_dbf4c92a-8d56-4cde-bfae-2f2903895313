"""
端侧CF-SRec模型

轻量级协同过滤序列推荐模型，专门负责序列建模和快速响应。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional


class CFSRec(nn.Module):
    """
    端侧CF-SRec模型
    
    专门负责序列建模，提取用户行为序列中的时序信息，
    生成64维用户表示用于云端传输。
    """
    
    def __init__(self, config: Dict):
        super(CFSRec, self).__init__()
        
        self.config = config
        self.item_num = config['item_num']
        self.hidden_size = config['hidden_size']
        self.max_seq_length = config['max_seq_length']
        self.num_attention_heads = config['num_attention_heads']
        self.num_hidden_layers = config['num_hidden_layers']
        self.dropout_prob = config['dropout_prob']
        
        # 用户表示维度（隐私传输）
        self.user_repr_dim = config.get('user_repr_dim', 64)
        
        # 嵌入层
        self.item_embeddings = nn.Embedding(self.item_num + 1, self.hidden_size, padding_idx=0)
        self.position_embeddings = nn.Embedding(self.max_seq_length, self.hidden_size)
        
        # Transformer编码器
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(self.hidden_size, self.num_attention_heads, self.dropout_prob)
            for _ in range(self.num_hidden_layers)
        ])
        
        # 层归一化
        self.LayerNorm = nn.LayerNorm(self.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(self.dropout_prob)
        
        # 用户表示提取器（用于云端传输）
        self.user_encoder = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout_prob),
            nn.Linear(self.hidden_size // 2, self.user_repr_dim)
        )
        
        # 预测头（端侧快速推荐）
        self.prediction_head = nn.Linear(self.hidden_size, self.item_num)
        
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            input_ids: 物品序列 [batch_size, seq_length]
            attention_mask: 注意力掩码 [batch_size, seq_length]
            
        Returns:
            包含序列表示、用户表示和预测logits的字典
        """
        batch_size, seq_length = input_ids.size()
        
        # 位置编码
        position_ids = torch.arange(seq_length, dtype=torch.long, device=input_ids.device)
        position_ids = position_ids.unsqueeze(0).expand(batch_size, -1)
        
        # 嵌入
        item_embeddings = self.item_embeddings(input_ids)
        position_embeddings = self.position_embeddings(position_ids)
        
        # 组合嵌入
        embeddings = item_embeddings + position_embeddings
        embeddings = self.LayerNorm(embeddings)
        embeddings = self.dropout(embeddings)
        
        # 注意力掩码
        if attention_mask is None:
            attention_mask = (input_ids != 0).float()
        
        # Transformer编码
        hidden_states = embeddings
        for transformer_block in self.transformer_blocks:
            hidden_states = transformer_block(hidden_states, attention_mask)
        
        # 序列表示（最后一个非padding位置）
        sequence_lengths = attention_mask.sum(dim=1) - 1  # 最后一个有效位置
        batch_indices = torch.arange(batch_size, device=input_ids.device)
        sequence_repr = hidden_states[batch_indices, sequence_lengths]
        
        # 用户表示（用于云端传输）
        user_repr = self.user_encoder(sequence_repr)
        
        # 端侧预测（快速响应）
        prediction_logits = self.prediction_head(sequence_repr)
        
        return {
            'sequence_repr': sequence_repr,
            'user_repr': user_repr,  # 64维，用于云端传输
            'prediction_logits': prediction_logits,
            'hidden_states': hidden_states
        }
    
    def extract_user_representation(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None) -> torch.Tensor:
        """
        提取用户表示用于云端传输
        
        Args:
            input_ids: 物品序列
            attention_mask: 注意力掩码
            
        Returns:
            64维用户表示
        """
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask)
            return outputs['user_repr']
    
    def quick_recommend(self, input_ids: torch.Tensor, attention_mask: torch.Tensor = None, top_k: int = 10) -> torch.Tensor:
        """
        端侧快速推荐
        
        Args:
            input_ids: 物品序列
            attention_mask: 注意力掩码
            top_k: 推荐物品数量
            
        Returns:
            推荐物品ID列表
        """
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask)
            logits = outputs['prediction_logits']
            
            # 排除已交互物品
            for i, seq in enumerate(input_ids):
                for item_id in seq:
                    if item_id != 0:  # 非padding
                        logits[i, item_id] = -float('inf')
            
            # Top-K推荐
            _, top_items = torch.topk(logits, top_k, dim=-1)
            return top_items


class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, hidden_size: int, num_attention_heads: int, dropout_prob: float):
        super(TransformerBlock, self).__init__()
        
        self.attention = MultiHeadSelfAttention(hidden_size, num_attention_heads, dropout_prob)
        self.feed_forward = FeedForward(hidden_size, dropout_prob)
        self.layer_norm1 = nn.LayerNorm(hidden_size, eps=1e-12)
        self.layer_norm2 = nn.LayerNorm(hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(dropout_prob)
    
    def forward(self, hidden_states: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        # 自注意力
        attention_output = self.attention(hidden_states, attention_mask)
        attention_output = self.dropout(attention_output)
        hidden_states = self.layer_norm1(hidden_states + attention_output)
        
        # 前馈网络
        feed_forward_output = self.feed_forward(hidden_states)
        feed_forward_output = self.dropout(feed_forward_output)
        hidden_states = self.layer_norm2(hidden_states + feed_forward_output)
        
        return hidden_states


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力"""
    
    def __init__(self, hidden_size: int, num_attention_heads: int, dropout_prob: float):
        super(MultiHeadSelfAttention, self).__init__()
        
        self.num_attention_heads = num_attention_heads
        self.attention_head_size = hidden_size // num_attention_heads
        self.all_head_size = self.num_attention_heads * self.attention_head_size
        
        self.query = nn.Linear(hidden_size, self.all_head_size)
        self.key = nn.Linear(hidden_size, self.all_head_size)
        self.value = nn.Linear(hidden_size, self.all_head_size)
        
        self.dropout = nn.Dropout(dropout_prob)
        self.dense = nn.Linear(hidden_size, hidden_size)
    
    def transpose_for_scores(self, x: torch.Tensor) -> torch.Tensor:
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)
    
    def forward(self, hidden_states: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        query_layer = self.transpose_for_scores(self.query(hidden_states))
        key_layer = self.transpose_for_scores(self.key(hidden_states))
        value_layer = self.transpose_for_scores(self.value(hidden_states))
        
        # 计算注意力分数
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / (self.attention_head_size ** 0.5)
        
        # 应用注意力掩码
        attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
        attention_scores = attention_scores + (1.0 - attention_mask) * -10000.0
        
        # 注意力权重
        attention_probs = F.softmax(attention_scores, dim=-1)
        attention_probs = self.dropout(attention_probs)
        
        # 应用注意力
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(*new_context_layer_shape)
        
        # 输出投影
        attention_output = self.dense(context_layer)
        return attention_output


class FeedForward(nn.Module):
    """前馈网络"""
    
    def __init__(self, hidden_size: int, dropout_prob: float):
        super(FeedForward, self).__init__()
        
        self.dense_1 = nn.Linear(hidden_size, hidden_size * 4)
        self.dense_2 = nn.Linear(hidden_size * 4, hidden_size)
        self.dropout = nn.Dropout(dropout_prob)
    
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        hidden_states = self.dense_1(hidden_states)
        hidden_states = F.gelu(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.dense_2(hidden_states)
        return hidden_states
