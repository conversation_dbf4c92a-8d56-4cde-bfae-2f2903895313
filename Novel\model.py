"""
主模型入口文件

提供简化的模型接口，方便快速使用端云协同推荐系统。
"""

import torch
from typing import Dict, List, Optional
import logging

from models import EdgeCloudCollaborativeModel

logger = logging.getLogger(__name__)


class NovelRecommendationSystem:
    """
    Novel推荐系统主类
    
    整合端云协同、隐私保护、知识蒸馏的完整推荐系统。
    提供简化的接口供用户使用。
    """
    
    def __init__(self, config: Dict):
        """
        初始化推荐系统
        
        Args:
            config: 配置字典，包含所有模型和系统配置
        """
        self.config = config
        self.device = config.get('device', 'cpu')
        
        # 初始化主协同模型
        self.model = EdgeCloudCollaborativeModel(config)
        self.model.to(self.device)
        
        # 模式设置
        self.inference_mode = config.get('inference_mode', 'collaborative')  # edge, cloud, collaborative
        
        logger.info(f"Initialized NovelRecommendationSystem with mode: {self.inference_mode}")
    
    def recommend(self, 
                 user_history: List[int],
                 top_k: int = 10,
                 mode: Optional[str] = None) -> List[int]:
        """
        为用户生成推荐
        
        Args:
            user_history: 用户历史交互物品ID列表
            top_k: 推荐物品数量
            mode: 推理模式，可选 'edge', 'cloud', 'collaborative'
            
        Returns:
            推荐物品ID列表
        """
        if mode is None:
            mode = self.inference_mode
        
        # 转换为tensor
        input_ids = torch.tensor([user_history], dtype=torch.long, device=self.device)
        
        # 生成推荐
        recommendations = self.model.recommend(input_ids, top_k=top_k, mode=mode)
        
        return recommendations
    
    def batch_recommend(self, 
                       user_histories: List[List[int]],
                       top_k: int = 10,
                       mode: Optional[str] = None) -> List[List[int]]:
        """
        批量推荐
        
        Args:
            user_histories: 多个用户的历史交互列表
            top_k: 推荐物品数量
            mode: 推理模式
            
        Returns:
            每个用户的推荐物品ID列表
        """
        if mode is None:
            mode = self.inference_mode
        
        # 批量处理
        max_len = max(len(history) for history in user_histories)
        
        # 填充序列
        padded_histories = []
        for history in user_histories:
            padded = history + [0] * (max_len - len(history))
            padded_histories.append(padded)
        
        # 转换为tensor
        input_ids = torch.tensor(padded_histories, dtype=torch.long, device=self.device)
        
        # 生成推荐
        batch_recommendations = []
        for i in range(input_ids.size(0)):
            user_input = input_ids[i:i+1]
            recommendations = self.model.recommend(user_input, top_k=top_k, mode=mode)
            batch_recommendations.append(recommendations)
        
        return batch_recommendations
    
    def train(self, 
             train_data,
             val_data=None,
             num_epochs: int = 10,
             enable_distillation: bool = True):
        """
        训练推荐系统
        
        Args:
            train_data: 训练数据
            val_data: 验证数据
            num_epochs: 训练轮数
            enable_distillation: 是否启用知识蒸馏
        """
        self.model.train()
        
        # 这里应该实现完整的训练循环
        # 简化示例
        logger.info(f"Training for {num_epochs} epochs with distillation={enable_distillation}")
        
        for epoch in range(num_epochs):
            # 训练逻辑
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            
            # 如果启用知识蒸馏
            if enable_distillation:
                # 执行蒸馏训练
                pass
        
        logger.info("Training completed")
    
    def evaluate(self, test_data) -> Dict[str, float]:
        """
        评估推荐系统
        
        Args:
            test_data: 测试数据
            
        Returns:
            评估指标字典
        """
        self.model.eval()
        
        # 这里应该实现完整的评估逻辑
        # 简化示例
        metrics = {
            'ndcg@10': 0.0,
            'hit_rate@10': 0.0,
            'recall@10': 0.0,
            'precision@10': 0.0
        }
        
        logger.info("Evaluation completed")
        return metrics
    
    def get_privacy_report(self) -> str:
        """获取隐私保护报告"""
        return self.model.privacy_manager.generate_privacy_report()
    
    def get_performance_stats(self) -> Dict[str, float]:
        """获取性能统计"""
        return self.model.get_performance_statistics()
    
    def switch_mode(self, mode: str):
        """
        切换推理模式
        
        Args:
            mode: 新的推理模式 ('edge', 'cloud', 'collaborative')
        """
        if mode not in ['edge', 'cloud', 'collaborative']:
            raise ValueError(f"Invalid mode: {mode}")
        
        self.inference_mode = mode
        logger.info(f"Switched to {mode} mode")
    
    def save_model(self, save_path: str):
        """保存模型"""
        self.model.save_model(save_path)
        logger.info(f"Model saved to {save_path}")
    
    def load_model(self, load_path: str):
        """加载模型"""
        self.model.load_model(load_path)
        logger.info(f"Model loaded from {load_path}")


def create_recommendation_system(config_path: str = None, **kwargs) -> NovelRecommendationSystem:
    """
    创建推荐系统的便捷函数
    
    Args:
        config_path: 配置文件路径
        **kwargs: 额外的配置参数
        
    Returns:
        NovelRecommendationSystem实例
    """
    if config_path:
        # 加载配置文件
        from utils import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config(config_path)
    else:
        # 使用默认配置
        config = {
            'device': 'cpu',
            'inference_mode': 'collaborative',
            'edge_model': {
                'item_num': 10000,
                'hidden_size': 64,
                'max_seq_length': 50,
                'num_attention_heads': 2,
                'num_hidden_layers': 2,
                'dropout_prob': 0.1,
                'user_repr_dim': 64
            },
            'cloud_model': {
                'llm_model_name': 'gpt2',
                'user_repr_dim': 64,
                'item_num': 10000,
                'max_seq_length': 50
            },
            'privacy': {
                'user_repr_dim': 64,
                'privacy_level': 'high',
                'enable_differential_privacy': True,
                'noise_scale': 0.1
            },
            'distillation': {
                'distillation_temperature': 4.0,
                'distillation_alpha': 0.7,
                'adaptive_distillation': True
            }
        }
    
    # 更新配置
    config.update(kwargs)
    
    return NovelRecommendationSystem(config)


# 便捷的使用示例
if __name__ == "__main__":
    # 创建推荐系统
    rec_system = create_recommendation_system()
    
    # 示例用户历史
    user_history = [1, 5, 23, 45, 67]
    
    # 生成推荐
    recommendations = rec_system.recommend(user_history, top_k=10)
    print(f"推荐结果: {recommendations}")
    
    # 切换到端侧模式
    rec_system.switch_mode('edge')
    edge_recommendations = rec_system.recommend(user_history, top_k=10)
    print(f"端侧推荐: {edge_recommendations}")
    
    # 获取隐私报告
    privacy_report = rec_system.get_privacy_report()
    print(f"隐私报告:\n{privacy_report}")
    
    # 获取性能统计
    stats = rec_system.get_performance_stats()
    print(f"性能统计: {stats}")
