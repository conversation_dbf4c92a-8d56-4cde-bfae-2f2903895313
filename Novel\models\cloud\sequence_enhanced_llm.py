"""
序列知识增强的LLM模型

将从端侧CF-SRec提取的用户表示集成到LLM中，
增强序列信息与LLM的融合，进行深度推荐优化。
"""

import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer, AutoConfig
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class SequenceEnhancedLLM(nn.Module):
    """
    序列知识增强的LLM推荐模型
    
    核心创新：
    1. 接收端侧64维用户表示，保护隐私
    2. 将序列信息融合到LLM中
    3. 增强LLM对用户行为序列的理解
    """
    
    def __init__(self, config: Dict):
        super(SequenceEnhancedLLM, self).__init__()
        
        self.config = config
        self.llm_model_name = config['llm_model_name']
        self.user_repr_dim = config.get('user_repr_dim', 64)
        self.item_num = config['item_num']
        self.max_seq_length = config['max_seq_length']
        
        # 加载预训练LLM
        self.llm_config = AutoConfig.from_pretrained(self.llm_model_name)
        self.llm = AutoModel.from_pretrained(self.llm_model_name)
        self.tokenizer = AutoTokenizer.from_pretrained(self.llm_model_name)
        
        # 冻结LLM参数（可选）
        if config.get('freeze_llm', False):
            for param in self.llm.parameters():
                param.requires_grad = False
        
        # 序列信息融合模块
        self.sequence_fusion = SequenceFusionModule(
            user_repr_dim=self.user_repr_dim,
            llm_hidden_size=self.llm_config.hidden_size,
            fusion_dim=config.get('fusion_dim', 256)
        )
        
        # 推荐头
        self.recommendation_head = RecommendationHead(
            input_dim=self.llm_config.hidden_size + config.get('fusion_dim', 256),
            hidden_dim=config.get('rec_hidden_dim', 512),
            item_num=self.item_num,
            dropout_prob=config.get('dropout_prob', 0.1)
        )
        
        # 特殊token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        logger.info(f"Initialized SequenceEnhancedLLM with {self.llm_model_name}")
    
    def forward(self, 
                user_repr: torch.Tensor,
                text_input: Dict[str, torch.Tensor],
                item_candidates: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            user_repr: 端侧传输的64维用户表示 [batch_size, user_repr_dim]
            text_input: 文本输入（包含input_ids, attention_mask等）
            item_candidates: 候选物品ID [batch_size, num_candidates]
            
        Returns:
            推荐结果字典
        """
        batch_size = user_repr.size(0)
        
        # LLM编码
        llm_outputs = self.llm(**text_input)
        llm_hidden_states = llm_outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]
        
        # 获取[CLS]或最后一个token的表示
        if hasattr(self.llm_config, 'pad_token_id'):
            # 找到最后一个非padding token
            attention_mask = text_input['attention_mask']
            sequence_lengths = attention_mask.sum(dim=1) - 1
            batch_indices = torch.arange(batch_size, device=user_repr.device)
            llm_repr = llm_hidden_states[batch_indices, sequence_lengths]
        else:
            # 使用平均池化
            llm_repr = llm_hidden_states.mean(dim=1)
        
        # 序列信息融合
        fused_repr = self.sequence_fusion(user_repr, llm_repr)
        
        # 组合表示
        combined_repr = torch.cat([llm_repr, fused_repr], dim=-1)
        
        # 推荐预测
        recommendation_logits = self.recommendation_head(combined_repr)
        
        return {
            'recommendation_logits': recommendation_logits,
            'llm_repr': llm_repr,
            'fused_repr': fused_repr,
            'combined_repr': combined_repr
        }
    
    def generate_recommendations(self, 
                               user_repr: torch.Tensor,
                               user_history: List[str],
                               top_k: int = 10) -> List[int]:
        """
        生成推荐结果
        
        Args:
            user_repr: 64维用户表示
            user_history: 用户历史行为文本描述
            top_k: 推荐数量
            
        Returns:
            推荐物品ID列表
        """
        self.eval()
        with torch.no_grad():
            # 构建文本输入
            text_prompt = self._build_recommendation_prompt(user_history)
            text_input = self.tokenizer(
                text_prompt,
                return_tensors='pt',
                padding=True,
                truncation=True,
                max_length=self.max_seq_length
            ).to(user_repr.device)
            
            # 前向传播
            outputs = self.forward(user_repr, text_input)
            logits = outputs['recommendation_logits']
            
            # Top-K推荐
            _, top_items = torch.topk(logits, top_k, dim=-1)
            return top_items.squeeze().tolist()
    
    def _build_recommendation_prompt(self, user_history: List[str]) -> str:
        """
        构建推荐提示文本
        
        Args:
            user_history: 用户历史行为描述
            
        Returns:
            推荐提示文本
        """
        history_text = " -> ".join(user_history[-10:])  # 最近10个行为
        
        prompt = f"""
        Based on the user's interaction history: {history_text}
        Please recommend items that the user might be interested in.
        Consider the sequential patterns and user preferences.
        """
        
        return prompt.strip()


class SequenceFusionModule(nn.Module):
    """
    序列信息融合模块
    
    将端侧用户表示与LLM表示进行深度融合
    """
    
    def __init__(self, user_repr_dim: int, llm_hidden_size: int, fusion_dim: int):
        super(SequenceFusionModule, self).__init__()
        
        self.user_repr_dim = user_repr_dim
        self.llm_hidden_size = llm_hidden_size
        self.fusion_dim = fusion_dim
        
        # 用户表示投影
        self.user_projection = nn.Sequential(
            nn.Linear(user_repr_dim, fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
        # LLM表示投影
        self.llm_projection = nn.Sequential(
            nn.Linear(llm_hidden_size, fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
        # 注意力融合
        self.attention_fusion = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
    
    def forward(self, user_repr: torch.Tensor, llm_repr: torch.Tensor) -> torch.Tensor:
        """
        融合用户表示和LLM表示
        
        Args:
            user_repr: 端侧用户表示 [batch_size, user_repr_dim]
            llm_repr: LLM表示 [batch_size, llm_hidden_size]
            
        Returns:
            融合后的表示 [batch_size, fusion_dim]
        """
        # 投影到相同维度
        user_proj = self.user_projection(user_repr)  # [batch_size, fusion_dim]
        llm_proj = self.llm_projection(llm_repr)      # [batch_size, fusion_dim]
        
        # 准备注意力输入
        user_proj = user_proj.unsqueeze(1)  # [batch_size, 1, fusion_dim]
        llm_proj = llm_proj.unsqueeze(1)    # [batch_size, 1, fusion_dim]
        
        # 注意力融合（用户表示作为query，LLM表示作为key和value）
        fused_repr, _ = self.attention_fusion(
            query=user_proj,
            key=llm_proj,
            value=llm_proj
        )
        
        fused_repr = fused_repr.squeeze(1)  # [batch_size, fusion_dim]
        
        # 输出投影
        output = self.output_projection(fused_repr)
        
        return output


class RecommendationHead(nn.Module):
    """推荐头"""
    
    def __init__(self, input_dim: int, hidden_dim: int, item_num: int, dropout_prob: float = 0.1):
        super(RecommendationHead, self).__init__()
        
        self.recommendation_layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_prob),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_prob),
            nn.Linear(hidden_dim // 2, item_num)
        )
    
    def forward(self, combined_repr: torch.Tensor) -> torch.Tensor:
        """
        生成推荐logits
        
        Args:
            combined_repr: 组合表示
            
        Returns:
            推荐logits [batch_size, item_num]
        """
        return self.recommendation_layers(combined_repr)
