"""
智能知识蒸馏模块

实现云端LLM向端侧CF-SRec的智能知识蒸馏，
包括自适应蒸馏策略和多层次知识传递。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import numpy as np
import logging

logger = logging.getLogger(__name__)


class IntelligentDistillation:
    """
    智能知识蒸馏器
    
    核心功能：
    1. 自适应蒸馏策略
    2. 多层次知识传递
    3. 动态温度调节
    4. 知识选择机制
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.temperature = config.get('temperature', 4.0)
        self.alpha = config.get('alpha', 0.7)  # 蒸馏损失权重
        self.beta = config.get('beta', 0.3)    # 任务损失权重
        
        # 自适应参数
        self.adaptive_temperature = config.get('adaptive_temperature', True)
        self.min_temperature = config.get('min_temperature', 1.0)
        self.max_temperature = config.get('max_temperature', 8.0)
        
        # 知识选择阈值
        self.knowledge_threshold = config.get('knowledge_threshold', 0.5)
        
        # 蒸馏策略
        self.distillation_strategy = config.get('distillation_strategy', 'adaptive')
        
        # 多层次蒸馏权重
        self.layer_weights = config.get('layer_weights', [0.2, 0.3, 0.5])  # 浅层、中层、深层
        
        logger.info(f"Initialized IntelligentDistillation with strategy: {self.distillation_strategy}")
    
    def distill_knowledge(self, 
                         teacher_outputs: Dict[str, torch.Tensor],
                         student_outputs: Dict[str, torch.Tensor],
                         targets: torch.Tensor,
                         epoch: int = 0) -> Dict[str, torch.Tensor]:
        """
        执行知识蒸馏
        
        Args:
            teacher_outputs: 教师模型输出（云端LLM）
            student_outputs: 学生模型输出（端侧CF-SRec）
            targets: 真实标签
            epoch: 当前训练轮次
            
        Returns:
            蒸馏损失字典
        """
        losses = {}
        
        # 自适应温度调节
        current_temperature = self._adaptive_temperature_schedule(epoch)
        
        # 1. 输出层知识蒸馏
        output_distill_loss = self._output_distillation(
            teacher_outputs['recommendation_logits'],
            student_outputs['prediction_logits'],
            current_temperature
        )
        losses['output_distill'] = output_distill_loss
        
        # 2. 特征层知识蒸馏
        if 'hidden_states' in teacher_outputs and 'hidden_states' in student_outputs:
            feature_distill_loss = self._feature_distillation(
                teacher_outputs['hidden_states'],
                student_outputs['hidden_states']
            )
            losses['feature_distill'] = feature_distill_loss
        
        # 3. 注意力知识蒸馏
        if 'attention_weights' in teacher_outputs and 'attention_weights' in student_outputs:
            attention_distill_loss = self._attention_distillation(
                teacher_outputs['attention_weights'],
                student_outputs['attention_weights']
            )
            losses['attention_distill'] = attention_distill_loss
        
        # 4. 任务损失
        task_loss = F.cross_entropy(student_outputs['prediction_logits'], targets)
        losses['task_loss'] = task_loss
        
        # 5. 智能知识选择
        if self.distillation_strategy == 'adaptive':
            selected_knowledge = self._intelligent_knowledge_selection(
                teacher_outputs, student_outputs, targets
            )
            losses['selected_knowledge'] = selected_knowledge
        
        # 6. 总损失
        total_loss = self._compute_total_loss(losses, current_temperature)
        losses['total_loss'] = total_loss
        losses['temperature'] = torch.tensor(current_temperature)
        
        return losses
    
    def _output_distillation(self, 
                           teacher_logits: torch.Tensor,
                           student_logits: torch.Tensor,
                           temperature: float) -> torch.Tensor:
        """
        输出层知识蒸馏
        
        Args:
            teacher_logits: 教师模型logits
            student_logits: 学生模型logits
            temperature: 蒸馏温度
            
        Returns:
            蒸馏损失
        """
        # 软化概率分布
        teacher_probs = F.softmax(teacher_logits / temperature, dim=-1)
        student_log_probs = F.log_softmax(student_logits / temperature, dim=-1)
        
        # KL散度损失
        distill_loss = F.kl_div(
            student_log_probs, 
            teacher_probs, 
            reduction='batchmean'
        ) * (temperature ** 2)
        
        return distill_loss
    
    def _feature_distillation(self, 
                            teacher_features: torch.Tensor,
                            student_features: torch.Tensor) -> torch.Tensor:
        """
        特征层知识蒸馏
        
        Args:
            teacher_features: 教师模型特征
            student_features: 学生模型特征
            
        Returns:
            特征蒸馏损失
        """
        # 维度对齐
        if teacher_features.size(-1) != student_features.size(-1):
            # 使用线性投影对齐维度
            projection = nn.Linear(
                student_features.size(-1), 
                teacher_features.size(-1)
            ).to(student_features.device)
            student_features = projection(student_features)
        
        # MSE损失
        feature_loss = F.mse_loss(student_features, teacher_features)
        
        return feature_loss
    
    def _attention_distillation(self, 
                              teacher_attention: torch.Tensor,
                              student_attention: torch.Tensor) -> torch.Tensor:
        """
        注意力知识蒸馏
        
        Args:
            teacher_attention: 教师模型注意力权重
            student_attention: 学生模型注意力权重
            
        Returns:
            注意力蒸馏损失
        """
        # 注意力权重对齐
        if teacher_attention.shape != student_attention.shape:
            # 简单的平均池化对齐
            if teacher_attention.size(1) > student_attention.size(1):
                # 教师模型头数更多，对学生模型进行扩展
                student_attention = student_attention.repeat(
                    1, teacher_attention.size(1) // student_attention.size(1), 1, 1
                )
            else:
                # 学生模型头数更多，对教师模型进行扩展
                teacher_attention = teacher_attention.repeat(
                    1, student_attention.size(1) // teacher_attention.size(1), 1, 1
                )
        
        # MSE损失
        attention_loss = F.mse_loss(student_attention, teacher_attention)
        
        return attention_loss
    
    def _intelligent_knowledge_selection(self, 
                                       teacher_outputs: Dict[str, torch.Tensor],
                                       student_outputs: Dict[str, torch.Tensor],
                                       targets: torch.Tensor) -> torch.Tensor:
        """
        智能知识选择
        
        根据教师模型的置信度和学生模型的学习难度，
        选择性地传递知识。
        
        Args:
            teacher_outputs: 教师模型输出
            student_outputs: 学生模型输出
            targets: 真实标签
            
        Returns:
            选择性知识蒸馏损失
        """
        teacher_logits = teacher_outputs['recommendation_logits']
        student_logits = student_outputs['prediction_logits']
        
        # 计算教师模型置信度
        teacher_probs = F.softmax(teacher_logits, dim=-1)
        teacher_confidence = torch.max(teacher_probs, dim=-1)[0]
        
        # 计算学生模型学习难度
        student_probs = F.softmax(student_logits, dim=-1)
        student_entropy = -torch.sum(student_probs * torch.log(student_probs + 1e-8), dim=-1)
        
        # 知识选择掩码
        # 高置信度的教师知识 + 高熵（难学习）的学生样本
        knowledge_mask = (teacher_confidence > self.knowledge_threshold) & \
                        (student_entropy > student_entropy.median())
        
        if knowledge_mask.sum() > 0:
            # 对选中的样本进行蒸馏
            selected_teacher_logits = teacher_logits[knowledge_mask]
            selected_student_logits = student_logits[knowledge_mask]
            
            selected_loss = self._output_distillation(
                selected_teacher_logits,
                selected_student_logits,
                self.temperature
            )
        else:
            selected_loss = torch.tensor(0.0, device=teacher_logits.device)
        
        return selected_loss
    
    def _adaptive_temperature_schedule(self, epoch: int) -> float:
        """
        自适应温度调度
        
        Args:
            epoch: 当前训练轮次
            
        Returns:
            当前温度值
        """
        if not self.adaptive_temperature:
            return self.temperature
        
        # 温度衰减策略：训练初期高温度，后期低温度
        decay_rate = 0.95
        current_temp = self.temperature * (decay_rate ** epoch)
        
        # 限制温度范围
        current_temp = max(self.min_temperature, min(self.max_temperature, current_temp))
        
        return current_temp
    
    def _compute_total_loss(self, 
                          losses: Dict[str, torch.Tensor],
                          temperature: float) -> torch.Tensor:
        """
        计算总损失
        
        Args:
            losses: 各项损失
            temperature: 当前温度
            
        Returns:
            总损失
        """
        total_loss = torch.tensor(0.0, device=list(losses.values())[0].device)
        
        # 任务损失
        if 'task_loss' in losses:
            total_loss += self.beta * losses['task_loss']
        
        # 蒸馏损失
        distill_loss = torch.tensor(0.0, device=total_loss.device)
        
        if 'output_distill' in losses:
            distill_loss += self.layer_weights[2] * losses['output_distill']
        
        if 'feature_distill' in losses:
            distill_loss += self.layer_weights[1] * losses['feature_distill']
        
        if 'attention_distill' in losses:
            distill_loss += self.layer_weights[0] * losses['attention_distill']
        
        if 'selected_knowledge' in losses:
            distill_loss += 0.5 * losses['selected_knowledge']
        
        total_loss += self.alpha * distill_loss
        
        return total_loss
    
    def update_distillation_strategy(self, 
                                   teacher_performance: float,
                                   student_performance: float,
                                   performance_gap: float) -> None:
        """
        根据性能差距更新蒸馏策略
        
        Args:
            teacher_performance: 教师模型性能
            student_performance: 学生模型性能
            performance_gap: 性能差距
        """
        if performance_gap > 0.1:  # 性能差距较大
            # 增加蒸馏强度
            self.alpha = min(0.9, self.alpha + 0.1)
            self.temperature = min(self.max_temperature, self.temperature + 0.5)
        elif performance_gap < 0.05:  # 性能差距较小
            # 减少蒸馏强度，增加任务学习
            self.alpha = max(0.3, self.alpha - 0.1)
            self.temperature = max(self.min_temperature, self.temperature - 0.5)
        
        logger.info(f"Updated distillation: α={self.alpha:.2f}, T={self.temperature:.2f}")


class ProgressiveDistillation:
    """
    渐进式知识蒸馏
    
    分阶段进行知识蒸馏，从简单到复杂
    """
    
    def __init__(self, distillation_manager: IntelligentDistillation):
        self.distillation_manager = distillation_manager
        self.current_stage = 0
        self.stages = ['basic', 'intermediate', 'advanced']
    
    def get_stage_config(self, stage: str) -> Dict:
        """获取阶段配置"""
        configs = {
            'basic': {
                'temperature': 6.0,
                'alpha': 0.8,
                'layer_weights': [0.1, 0.2, 0.7]
            },
            'intermediate': {
                'temperature': 4.0,
                'alpha': 0.6,
                'layer_weights': [0.2, 0.3, 0.5]
            },
            'advanced': {
                'temperature': 2.0,
                'alpha': 0.4,
                'layer_weights': [0.3, 0.3, 0.4]
            }
        }
        return configs.get(stage, configs['basic'])
    
    def advance_stage(self) -> bool:
        """推进到下一阶段"""
        if self.current_stage < len(self.stages) - 1:
            self.current_stage += 1
            stage_config = self.get_stage_config(self.stages[self.current_stage])
            
            # 更新蒸馏参数
            self.distillation_manager.temperature = stage_config['temperature']
            self.distillation_manager.alpha = stage_config['alpha']
            self.distillation_manager.layer_weights = stage_config['layer_weights']
            
            logger.info(f"Advanced to stage: {self.stages[self.current_stage]}")
            return True
        return False
