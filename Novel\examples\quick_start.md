# LLM-SRec 快速开始指南

本指南将带您从零开始，快速上手LLM-SRec推荐系统框架。

## 🎯 目标

通过本指南，您将学会：
- 安装和配置LLM-SRec环境
- 准备和处理推荐数据集
- 运行您的第一个推荐模型实验
- 理解实验结果和输出

## 📋 前置要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 至少8GB内存
- 至少10GB可用磁盘空间

## 🚀 第一步：环境安装

### 1.1 克隆项目

```bash
git clone <repository-url>
cd Novel
```

### 1.2 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 验证安装
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import transformers; print(f'Transformers版本: {transformers.__version__}')"
```

### 1.3 验证GPU支持（可选）

```bash
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

## 📊 第二步：数据集准备

### 2.1 查看可用数据集

```bash
python prepare_data.py --list
```

您将看到类似输出：
```
Available Datasets:
==================================================
  Movies_and_TV
    Description: Amazon Movies and TV reviews dataset
    Source: amazon
    Task Type: sequential_recommendation
    Downloaded: ✗
    Processed: ✗

  Beauty
    Description: Amazon Beauty products reviews dataset
    Source: amazon
    Task Type: sequential_recommendation
    Downloaded: ✗
    Processed: ✗
    
  ml-1m
    Description: MovieLens 1M dataset from HuggingFace
    Source: huggingface
    Task Type: rating_prediction
    Downloaded: ✗
    Processed: ✗
```

### 2.2 准备第一个数据集

我们从最小的数据集开始：

```bash
# 准备MovieLens 100K数据集（较小，适合快速测试）
python prepare_data.py --dataset ml-100k

# 检查准备状态
python prepare_data.py --status ml-100k
```

数据准备过程包括：
1. 自动下载原始数据
2. 数据清洗和过滤
3. 序列化处理
4. 训练/验证/测试集分割

## 🎮 第三步：运行第一个实验

### 3.1 快速测试实验

首先运行一个快速测试，验证系统功能：

```bash
python run_experiment.py --config experiments/configs/quick_test.yaml
```

这个实验会：
- 使用CPU进行计算（无需GPU）
- 训练3个epoch（约2-5分钟）
- 使用最小的模型配置

### 3.2 查看实验结果

实验完成后，您会看到类似输出：

```
Experiment completed successfully!

Final Evaluation Results:
  ndcg@5: 0.1234
  hit_rate@5: 0.2345
  recall@5: 0.1567

Experiment time: 156.78 seconds
Results saved to: ./experiments/runs/quick_test
```

### 3.3 检查输出文件

```bash
# 查看实验目录结构
ls -la experiments/runs/quick_test/

# 查看训练历史
cat experiments/runs/quick_test/results/training_history.csv

# 查看评估指标
cat experiments/runs/quick_test/results/evaluation_metrics.csv
```

## 🔧 第四步：运行完整实验

### 4.1 准备更大的数据集

```bash
# 准备Movies_and_TV数据集
python prepare_data.py --dataset Movies_and_TV
```

### 4.2 运行完整实验

```bash
python run_experiment.py \
    --dataset Movies_and_TV \
    --experiment-name my_first_experiment \
    --batch-size 32 \
    --num-epochs 10
```

### 4.3 监控训练过程

在另一个终端中，您可以监控训练日志：

```bash
tail -f experiments/runs/my_first_experiment/experiment.log
```

## 📈 第五步：理解结果

### 5.1 关键指标说明

- **NDCG@K**: 归一化折扣累积增益，衡量排序质量
- **Hit Rate@K**: 命中率，前K个推荐中包含真实物品的比例
- **Recall@K**: 召回率，前K个推荐中真实物品的比例
- **MRR@K**: 平均倒数排名，衡量第一个相关物品的位置

### 5.2 结果文件说明

```
experiments/runs/my_first_experiment/
├── results/
│   ├── latest_results.json      # 完整实验结果
│   ├── training_history.csv     # 每个epoch的训练指标
│   ├── evaluation_metrics.csv   # 最终评估指标
│   └── statistics.json          # 数据集统计信息
├── plots/
│   ├── training_curves.png      # 训练损失和指标曲线
│   └── evaluation_metrics.png   # 评估指标柱状图
├── checkpoints/                 # 模型检查点
│   ├── model_best.pth          # 最佳模型
│   └── model_latest.pth        # 最新模型
└── experiment.log              # 详细日志
```

### 5.3 查看可视化结果

如果您的环境支持图形界面，可以查看生成的图表：

```bash
# 在支持图形的环境中
open experiments/runs/my_first_experiment/plots/training_curves.png
open experiments/runs/my_first_experiment/plots/evaluation_metrics.png
```

## 🎛️ 第六步：自定义实验

### 6.1 修改训练参数

```bash
python run_experiment.py \
    --dataset Movies_and_TV \
    --experiment-name custom_experiment \
    --batch-size 64 \
    --learning-rate 0.001 \
    --num-epochs 20 \
    --device cuda:0
```

### 6.2 使用不同数据集

```bash
# 美妆产品推荐
python run_experiment.py --dataset Beauty --experiment-name beauty_exp

# 电影推荐
python run_experiment.py --dataset ml-1m --experiment-name movie_exp
```

### 6.3 分步骤执行

```bash
# 1. 仅准备数据
python run_experiment.py --dataset Beauty --prepare-data-only

# 2. 仅训练模型
python run_experiment.py \
    --config experiments/configs/beauty_experiment.yaml \
    --train-only

# 3. 仅评估模型
python run_experiment.py \
    --config experiments/configs/beauty_experiment.yaml \
    --eval-only \
    --model-path experiments/runs/beauty_experiment/checkpoints/model_best.pth
```

## 🐛 常见问题解决

### 问题1：内存不足

```bash
# 解决方案：减少批次大小
python run_experiment.py --dataset Movies_and_TV --batch-size 16

# 或使用CPU
python run_experiment.py --dataset Movies_and_TV --device cpu
```

### 问题2：数据下载失败

```bash
# 解决方案：重试下载
python prepare_data.py --dataset Movies_and_TV --force-download

# 检查网络连接和磁盘空间
df -h
ping google.com
```

### 问题3：训练中断

```bash
# 解决方案：从检查点恢复
python train_model.py \
    --config experiments/configs/my_experiment.yaml \
    --resume experiments/runs/my_experiment/checkpoints/model_latest.pth
```

## 🎉 恭喜！

您已经成功：
- ✅ 安装了LLM-SRec环境
- ✅ 准备了推荐数据集
- ✅ 运行了第一个推荐实验
- ✅ 理解了实验结果

## 🚀 下一步

现在您可以：

1. **探索更多数据集** - 尝试不同的推荐场景
2. **调优模型参数** - 提升推荐性能
3. **运行消融实验** - 理解各组件的贡献
4. **部署推荐服务** - 将模型投入实际应用

继续阅读：
- [数据集准备指南](dataset_preparation.md) - 深入了解数据处理
- [模型训练指南](model_training.md) - 掌握高级训练技巧
- [示例脚本](scripts/) - 查看更多实用脚本

祝您使用愉快！🎊
