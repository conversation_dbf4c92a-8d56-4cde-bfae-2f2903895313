"""
模型定义模块

包含端云协同推荐系统的所有模型实现：
- 端侧模型：轻量级CF-SRec，负责序列建模和快速响应
- 云端模型：序列增强LLM，负责深度推荐优化
- 协同模型：端云协同架构
- 知识蒸馏：智能蒸馏机制
- 隐私保护：隐私管理和安全传输
"""

# 端侧模型
from .client.cf_srec import CFSRec

# 云端模型  
from .cloud.sequence_enhanced_llm import SequenceEnhancedLLM
from .cloud.llm_cloud_server import LLMCloudServer

# 协同模型
from .collaborative.collaborative_model import CollaborativeModel
from .collaborative.enhanced_collaborative_model import EnhancedCollaborativeModel
from .collaborative.edge_cloud_collaborative import EdgeCloudCollaborativeModel

# 知识蒸馏
from .knowledge_distillation.intelligent_distillation import IntelligentDistillation

# 隐私保护
from .privacy.privacy_manager import PrivacyManager

__all__ = [
    # 端侧模型
    'CFSRec',
    
    # 云端模型
    'SequenceEnhancedLLM',
    'LLMCloudServer',
    
    # 协同模型
    'CollaborativeModel',
    'EnhancedCollaborativeModel',
    'EdgeCloudCollaborativeModel',

    # 知识蒸馏
    'IntelligentDistillation',
    
    # 隐私保护
    'PrivacyManager'
]
