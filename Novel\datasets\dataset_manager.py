"""
数据集管理器

统一管理数据集的下载、预处理和加载，支持多种数据源。
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from .downloaders import HuggingFaceDownloader, AmazonDownloader
from .preprocessor import DataPreprocessor
from .data_loader import RecommendationDataLoader

logger = logging.getLogger(__name__)


@dataclass
class DatasetConfig:
    """数据集配置"""
    name: str
    source: str  # 'huggingface', 'amazon', 'local'
    url_or_path: str
    description: str
    task_type: str  # 'sequential_recommendation', 'rating_prediction'
    min_interactions: int = 5
    max_sequence_length: int = 128
    train_ratio: float = 0.8
    val_ratio: float = 0.1
    test_ratio: float = 0.1


class DatasetManager:
    """数据集管理器"""
    
    # 预定义的数据集配置
    PREDEFINED_DATASETS = {
        "Movies_and_TV": DatasetConfig(
            name="Movies_and_TV",
            source="amazon",
            url_or_path="http://snap.stanford.edu/data/amazon/productGraph/categoryFiles/reviews_Movies_and_TV_5.json.gz",
            description="Amazon Movies and TV reviews dataset",
            task_type="sequential_recommendation"
        ),
        "Beauty": DatasetConfig(
            name="Beauty",
            source="amazon", 
            url_or_path="http://snap.stanford.edu/data/amazon/productGraph/categoryFiles/reviews_Beauty_5.json.gz",
            description="Amazon Beauty products reviews dataset",
            task_type="sequential_recommendation"
        ),
        "ml-1m": DatasetConfig(
            name="ml-1m",
            source="huggingface",
            url_or_path="reczoo/movielens_x1",
            description="MovieLens 1M dataset from HuggingFace",
            task_type="rating_prediction"
        ),
        "ml-100k": DatasetConfig(
            name="ml-100k",
            source="huggingface", 
            url_or_path="reczoo/movielens_x0",
            description="MovieLens 100K dataset from HuggingFace",
            task_type="rating_prediction"
        )
    }
    
    def __init__(self, data_dir: str = "./data"):
        """
        初始化数据集管理器
        
        Args:
            data_dir: 数据存储根目录
        """
        self.data_dir = Path(data_dir)
        self.raw_data_dir = self.data_dir / "raw"
        self.processed_data_dir = self.data_dir / "processed"
        
        # 创建目录
        self.raw_data_dir.mkdir(parents=True, exist_ok=True)
        self.processed_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化下载器
        self.downloaders = {
            'huggingface': HuggingFaceDownloader(),
            'amazon': AmazonDownloader(),
        }
        
        # 初始化预处理器
        self.preprocessor = DataPreprocessor()
        
        logger.info(f"DatasetManager initialized with data_dir: {self.data_dir}")
    
    def list_available_datasets(self) -> List[str]:
        """列出所有可用的数据集"""
        return list(self.PREDEFINED_DATASETS.keys())
    
    def get_dataset_info(self, dataset_name: str) -> Optional[DatasetConfig]:
        """获取数据集信息"""
        return self.PREDEFINED_DATASETS.get(dataset_name)
    
    def is_dataset_downloaded(self, dataset_name: str) -> bool:
        """检查数据集是否已下载"""
        if dataset_name not in self.PREDEFINED_DATASETS:
            return False
        
        raw_path = self.raw_data_dir / dataset_name
        return raw_path.exists() and any(raw_path.iterdir())
    
    def is_dataset_processed(self, dataset_name: str) -> bool:
        """检查数据集是否已预处理"""
        if dataset_name not in self.PREDEFINED_DATASETS:
            return False
        
        processed_path = self.processed_data_dir / dataset_name
        return processed_path.exists() and any(processed_path.iterdir())
    
    def download_dataset(self, dataset_name: str, force_download: bool = False) -> bool:
        """
        下载数据集
        
        Args:
            dataset_name: 数据集名称
            force_download: 是否强制重新下载
            
        Returns:
            是否下载成功
        """
        if dataset_name not in self.PREDEFINED_DATASETS:
            logger.error(f"Unknown dataset: {dataset_name}")
            return False
        
        if not force_download and self.is_dataset_downloaded(dataset_name):
            logger.info(f"Dataset {dataset_name} already downloaded")
            return True
        
        config = self.PREDEFINED_DATASETS[dataset_name]
        downloader = self.downloaders.get(config.source)
        
        if not downloader:
            logger.error(f"No downloader available for source: {config.source}")
            return False
        
        try:
            output_dir = self.raw_data_dir / dataset_name
            output_dir.mkdir(exist_ok=True)
            
            logger.info(f"Downloading dataset {dataset_name} from {config.source}")
            success = downloader.download(config.url_or_path, str(output_dir))
            
            if success:
                logger.info(f"Successfully downloaded dataset {dataset_name}")
                # 保存数据集元信息
                self._save_dataset_metadata(dataset_name, config)
            else:
                logger.error(f"Failed to download dataset {dataset_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error downloading dataset {dataset_name}: {e}")
            return False
    
    def preprocess_dataset(self, dataset_name: str, config_override: Optional[Dict] = None) -> bool:
        """
        预处理数据集
        
        Args:
            dataset_name: 数据集名称
            config_override: 覆盖默认配置的参数
            
        Returns:
            是否预处理成功
        """
        if dataset_name not in self.PREDEFINED_DATASETS:
            logger.error(f"Unknown dataset: {dataset_name}")
            return False
        
        if not self.is_dataset_downloaded(dataset_name):
            logger.error(f"Dataset {dataset_name} not downloaded yet")
            return False
        
        if self.is_dataset_processed(dataset_name):
            logger.info(f"Dataset {dataset_name} already processed")
            return True
        
        try:
            config = self.PREDEFINED_DATASETS[dataset_name]
            
            # 应用配置覆盖
            if config_override:
                for key, value in config_override.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
            
            raw_path = self.raw_data_dir / dataset_name
            processed_path = self.processed_data_dir / dataset_name
            processed_path.mkdir(exist_ok=True)
            
            logger.info(f"Preprocessing dataset {dataset_name}")
            success = self.preprocessor.preprocess(
                raw_data_path=str(raw_path),
                output_path=str(processed_path),
                config=config
            )
            
            if success:
                logger.info(f"Successfully preprocessed dataset {dataset_name}")
            else:
                logger.error(f"Failed to preprocess dataset {dataset_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error preprocessing dataset {dataset_name}: {e}")
            return False
    
    def prepare_dataset(self, dataset_name: str, force_download: bool = False, 
                       config_override: Optional[Dict] = None) -> bool:
        """
        准备数据集（下载+预处理）
        
        Args:
            dataset_name: 数据集名称
            force_download: 是否强制重新下载
            config_override: 覆盖默认配置的参数
            
        Returns:
            是否准备成功
        """
        logger.info(f"Preparing dataset: {dataset_name}")
        
        # 下载数据集
        if not self.download_dataset(dataset_name, force_download):
            return False
        
        # 预处理数据集
        if not self.preprocess_dataset(dataset_name, config_override):
            return False
        
        logger.info(f"Dataset {dataset_name} prepared successfully")
        return True
    
    def get_data_loader(self, dataset_name: str, batch_size: int = 32, 
                       split: str = 'train') -> Optional[RecommendationDataLoader]:
        """
        获取数据加载器
        
        Args:
            dataset_name: 数据集名称
            batch_size: 批次大小
            split: 数据分割 ('train', 'val', 'test')
            
        Returns:
            数据加载器实例
        """
        if not self.is_dataset_processed(dataset_name):
            logger.error(f"Dataset {dataset_name} not processed yet")
            return None
        
        try:
            processed_path = self.processed_data_dir / dataset_name
            return RecommendationDataLoader(
                data_path=str(processed_path),
                batch_size=batch_size,
                split=split
            )
        except Exception as e:
            logger.error(f"Error creating data loader for {dataset_name}: {e}")
            return None
    
    def _save_dataset_metadata(self, dataset_name: str, config: DatasetConfig):
        """保存数据集元信息"""
        metadata = {
            'name': config.name,
            'source': config.source,
            'description': config.description,
            'task_type': config.task_type,
            'download_time': str(Path().cwd()),  # 简化的时间戳
        }
        
        metadata_path = self.raw_data_dir / dataset_name / 'metadata.json'
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
