"""
实验运行器

负责执行各种实验配置，包括数据准备、模型训练、评估等。
"""

import os
import logging
import time
import torch
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from ..datasets import DatasetManager, create_data_loaders
from ..training.collaborative_trainer import CollaborativeTrainer
from .config_manager import ConfigManager
from .result_tracker import ResultTracker

logger = logging.getLogger(__name__)


class ExperimentRunner:
    """实验运行器"""
    
    def __init__(self, config_path: Optional[str] = None, experiment_name: Optional[str] = None):
        """
        初始化实验运行器
        
        Args:
            config_path: 配置文件路径
            experiment_name: 实验名称
        """
        self.config_manager = ConfigManager()
        
        if config_path:
            self.config = self.config_manager.load_config(config_path)
        else:
            self.config = self.config_manager.get_default_config()
        
        if experiment_name:
            self.config['experiment_name'] = experiment_name
        
        # 设置实验目录
        self.experiment_dir = Path("./experiments/runs") / self.config['experiment_name']
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.dataset_manager = DatasetManager(self.config['data']['data_dir'])
        self.result_tracker = ResultTracker(str(self.experiment_dir))
        
        # 设置日志
        self._setup_logging()
        
        logger.info(f"Initialized experiment: {self.config['experiment_name']}")
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.experiment_dir / "experiment.log"
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # 添加到根日志器
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        root_logger.setLevel(logging.INFO)
    
    def run_full_experiment(self) -> Dict[str, Any]:
        """
        运行完整实验流程
        
        Returns:
            实验结果
        """
        logger.info("Starting full experiment...")
        start_time = time.time()
        
        try:
            # 1. 准备数据集
            logger.info("Step 1: Preparing dataset...")
            if not self._prepare_dataset():
                raise RuntimeError("Dataset preparation failed")
            
            # 2. 创建数据加载器
            logger.info("Step 2: Creating data loaders...")
            train_loader, val_loader, test_loader = self._create_data_loaders()
            
            # 3. 初始化模型和训练器
            logger.info("Step 3: Initializing model and trainer...")
            trainer = self._create_trainer(train_loader, val_loader)
            
            # 4. 训练模型
            logger.info("Step 4: Training model...")
            training_results = trainer.train()
            
            # 5. 评估模型
            logger.info("Step 5: Evaluating model...")
            evaluation_results = trainer.evaluate(test_loader)
            
            # 6. 保存结果
            logger.info("Step 6: Saving results...")
            results = {
                'training_results': training_results,
                'evaluation_results': evaluation_results,
                'config': self.config,
                'experiment_time': time.time() - start_time
            }
            
            self.result_tracker.save_results(results)
            
            logger.info(f"Experiment completed successfully in {results['experiment_time']:.2f} seconds")
            return results
            
        except Exception as e:
            logger.error(f"Experiment failed: {e}")
            raise
    
    def run_data_preparation_only(self) -> bool:
        """仅运行数据准备"""
        logger.info("Running data preparation only...")
        return self._prepare_dataset()
    
    def run_training_only(self) -> Dict[str, Any]:
        """仅运行训练（假设数据已准备）"""
        logger.info("Running training only...")
        
        # 检查数据是否已准备
        dataset_name = self.config['data']['dataset']
        if not self.dataset_manager.is_dataset_processed(dataset_name):
            raise RuntimeError(f"Dataset {dataset_name} not prepared. Run data preparation first.")
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = self._create_data_loaders()
        
        # 创建训练器
        trainer = self._create_trainer(train_loader, val_loader)
        
        # 训练
        training_results = trainer.train()
        
        # 保存训练结果
        self.result_tracker.save_training_results(training_results)
        
        return training_results
    
    def run_evaluation_only(self, model_path: str) -> Dict[str, Any]:
        """仅运行评估（使用已训练的模型）"""
        logger.info(f"Running evaluation only with model: {model_path}")
        
        # 检查数据是否已准备
        dataset_name = self.config['data']['dataset']
        if not self.dataset_manager.is_dataset_processed(dataset_name):
            raise RuntimeError(f"Dataset {dataset_name} not prepared. Run data preparation first.")
        
        # 创建数据加载器
        _, _, test_loader = self._create_data_loaders()
        
        # 创建训练器并加载模型
        trainer = self._create_trainer(None, None)
        trainer.load_model(model_path)
        
        # 评估
        evaluation_results = trainer.evaluate(test_loader)
        
        # 保存评估结果
        self.result_tracker.save_evaluation_results(evaluation_results)
        
        return evaluation_results
    
    def _prepare_dataset(self) -> bool:
        """准备数据集"""
        dataset_name = self.config['data']['dataset']
        
        logger.info(f"Preparing dataset: {dataset_name}")
        
        # 检查数据集是否在预定义列表中
        if dataset_name not in self.dataset_manager.list_available_datasets():
            logger.error(f"Unknown dataset: {dataset_name}")
            return False
        
        # 准备数据集（下载+预处理）
        config_override = {
            'min_interactions': self.config['data']['min_interactions'],
            'max_sequence_length': self.config['data']['max_sequence_length'],
            'train_ratio': self.config['data']['train_ratio'],
            'val_ratio': self.config['data']['val_ratio'],
            'test_ratio': self.config['data']['test_ratio']
        }
        
        success = self.dataset_manager.prepare_dataset(
            dataset_name=dataset_name,
            force_download=False,
            config_override=config_override
        )
        
        if success:
            logger.info(f"Dataset {dataset_name} prepared successfully")
        else:
            logger.error(f"Failed to prepare dataset {dataset_name}")
        
        return success
    
    def _create_data_loaders(self):
        """创建数据加载器"""
        dataset_name = self.config['data']['dataset']
        processed_data_path = self.dataset_manager.processed_data_dir / dataset_name
        
        train_loader, val_loader, test_loader = create_data_loaders(
            data_path=str(processed_data_path),
            batch_size=self.config['training']['batch_size'],
            max_length=self.config['data']['max_sequence_length'],
            num_workers=self.config['optimization']['dataloader_num_workers']
        )
        
        logger.info(f"Created data loaders - Train: {len(train_loader)}, Val: {len(val_loader)}, Test: {len(test_loader)}")
        
        return train_loader, val_loader, test_loader
    
    def _create_trainer(self, train_loader=None, val_loader=None):
        """创建训练器"""
        # 更新配置中的模型参数
        if train_loader:
            self.config['small_model']['item_num'] = train_loader.num_items
            self.config['small_model']['max_sequence_length'] = self.config['data']['max_sequence_length']
        
        trainer = CollaborativeTrainer(
            config=self.config,
            output_dir=str(self.experiment_dir)
        )
        
        return trainer
    
    def get_experiment_status(self) -> Dict[str, Any]:
        """获取实验状态"""
        dataset_name = self.config['data']['dataset']
        
        status = {
            'experiment_name': self.config['experiment_name'],
            'dataset': dataset_name,
            'dataset_downloaded': self.dataset_manager.is_dataset_downloaded(dataset_name),
            'dataset_processed': self.dataset_manager.is_dataset_processed(dataset_name),
            'experiment_dir': str(self.experiment_dir),
            'config': self.config
        }
        
        return status
    
    def list_available_datasets(self) -> list:
        """列出可用数据集"""
        return self.dataset_manager.list_available_datasets()
    
    def get_dataset_info(self, dataset_name: str) -> Optional[Dict]:
        """获取数据集信息"""
        config = self.dataset_manager.get_dataset_info(dataset_name)
        if config:
            return {
                'name': config.name,
                'source': config.source,
                'description': config.description,
                'task_type': config.task_type
            }
        return None
