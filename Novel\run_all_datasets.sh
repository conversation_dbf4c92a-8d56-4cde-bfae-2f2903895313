#!/bin/bash

# 批量运行所有数据集实验脚本 (Linux服务器版)
# 用法: bash run_all_datasets.sh [gpu_id]

set -e  # 遇到错误时退出

# 获取GPU ID参数，默认为0
GPU_ID=${1:-0}

# 使用python3或python
PYTHON_CMD="python3"
command -v python3 >/dev/null 2>&1 || PYTHON_CMD="python"

echo "=========================================="
echo "LLM-SRec 批量数据集实验 (Linux服务器)"
echo "使用GPU: $GPU_ID"
echo "=========================================="

# 检查GPU可用性
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "GPU状态:"
    nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits | head -5
    echo ""
else
    echo "警告: 未检测到NVIDIA GPU，将使用CPU训练"
    GPU_ID="cpu"
fi

# 定义数据集列表（按大小排序）
DATASETS=("ml-100k" "Beauty" "Movies_and_TV" "ml-1m")

# 定义实验参数
BATCH_SIZE=32
LEARNING_RATE=1e-4
EPOCHS=10  # 减少epoch数以适应服务器环境

# 创建结果目录
RESULTS_DIR="experiments/batch_results/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$RESULTS_DIR"

echo "结果将保存到: $RESULTS_DIR"
echo ""

# 记录开始时间
START_TIME=$(date +%s)

# 遍历所有数据集
for dataset in "${DATASETS[@]}"; do
    echo "=========================================="
    echo "开始处理数据集: $dataset"
    echo "时间: $(date)"
    echo "=========================================="
    
    # 记录单个数据集开始时间
    DATASET_START_TIME=$(date +%s)
    
    # 实验名称
    EXPERIMENT_NAME="batch_${dataset}_$(date +%Y%m%d_%H%M%S)"
    
    echo "实验名称: $EXPERIMENT_NAME"
    echo "批次大小: $BATCH_SIZE"
    echo "学习率: $LEARNING_RATE"
    echo "训练轮数: $EPOCHS"
    echo "设备: $GPU_ID"
    echo ""
    
    # 准备数据集
    echo "步骤 1/3: 准备数据集..."
    if ! $PYTHON_CMD prepare_data.py --dataset "$dataset"; then
        echo "错误: 数据集 $dataset 准备失败，跳过"
        continue
    fi
    
    # 训练模型
    echo "步骤 2/3: 训练模型..."
    if [ "$GPU_ID" = "cpu" ]; then
        DEVICE_ARG="--device cpu"
    else
        DEVICE_ARG="--device cuda:$GPU_ID"
    fi
    
    if ! $PYTHON_CMD run_experiment.py \
        --dataset "$dataset" \
        --experiment-name "$EXPERIMENT_NAME" \
        --batch-size "$BATCH_SIZE" \
        --learning-rate "$LEARNING_RATE" \
        --num-epochs "$EPOCHS" \
        $DEVICE_ARG; then
        echo "错误: 数据集 $dataset 训练失败，跳过"
        continue
    fi
    
    # 复制结果到批量结果目录
    echo "步骤 3/3: 保存结果..."
    if [ -d "experiments/runs/$EXPERIMENT_NAME" ]; then
        cp -r "experiments/runs/$EXPERIMENT_NAME" "$RESULTS_DIR/"
        echo "结果已保存到: $RESULTS_DIR/$EXPERIMENT_NAME"
    fi
    
    # 计算单个数据集用时
    DATASET_END_TIME=$(date +%s)
    DATASET_DURATION=$((DATASET_END_TIME - DATASET_START_TIME))
    
    echo "数据集 $dataset 完成，用时: ${DATASET_DURATION}秒"
    
    # 显示GPU内存使用情况
    if [ "$GPU_ID" != "cpu" ] && command -v nvidia-smi >/dev/null 2>&1; then
        echo "当前GPU内存使用:"
        nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits -i $GPU_ID
    fi
    
    echo ""
done

# 计算总用时
END_TIME=$(date +%s)
TOTAL_DURATION=$((END_TIME - START_TIME))

echo "=========================================="
echo "批量实验完成！"
echo "总用时: ${TOTAL_DURATION}秒 ($(($TOTAL_DURATION / 60))分钟)"
echo "结果保存在: $RESULTS_DIR"
echo "=========================================="

# 生成简单的汇总报告
echo "生成汇总报告..."
REPORT_FILE="$RESULTS_DIR/batch_summary.txt"

cat > "$REPORT_FILE" << EOF
LLM-SRec 批量实验汇总报告
生成时间: $(date)
总用时: ${TOTAL_DURATION}秒 ($(($TOTAL_DURATION / 60))分钟)
使用设备: $GPU_ID

实验结果:
EOF

# 遍历结果目录，提取关键指标
for exp_dir in "$RESULTS_DIR"/batch_*; do
    if [ -d "$exp_dir" ]; then
        exp_name=$(basename "$exp_dir")
        results_file="$exp_dir/results/latest_results.json"
        
        if [ -f "$results_file" ]; then
            echo "" >> "$REPORT_FILE"
            echo "实验: $exp_name" >> "$REPORT_FILE"
            
            # 提取关键指标
            $PYTHON_CMD -c "
import json
import sys
try:
    with open('$results_file', 'r') as f:
        results = json.load(f)
    eval_results = results.get('evaluation_results', {})
    dataset = results.get('config', {}).get('data', {}).get('dataset', 'Unknown')
    exp_time = results.get('experiment_time', 0)
    
    print(f'数据集: {dataset}')
    print(f'训练时间: {exp_time:.2f}秒')
    
    # 显示主要指标
    key_metrics = ['ndcg@10', 'hit_rate@10', 'recall@10']
    for metric in key_metrics:
        if metric in eval_results:
            print(f'{metric}: {eval_results[metric]:.4f}')
except Exception as e:
    print(f'读取结果失败: {e}')
" >> "$REPORT_FILE"
        fi
    fi
done

echo "汇总报告已生成: $REPORT_FILE"
echo ""
echo "查看汇总报告:"
cat "$REPORT_FILE"

echo ""
echo "Linux服务器使用提示："
echo "- 可以使用 'nohup bash run_all_datasets.sh > batch_log.txt 2>&1 &' 在后台运行"
echo "- 使用 'tail -f batch_log.txt' 监控进度"
echo "- 使用 'nvidia-smi' 监控GPU使用情况"
