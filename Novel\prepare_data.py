#!/usr/bin/env python3
"""
数据集准备脚本

专门用于下载和预处理数据集的独立脚本。

使用示例:
    # 准备单个数据集
    python prepare_data.py --dataset Movies_and_TV

    # 准备多个数据集
    python prepare_data.py --dataset Movies_and_TV Beauty ml-1m

    # 强制重新下载
    python prepare_data.py --dataset Movies_and_TV --force-download

    # 列出所有可用数据集
    python prepare_data.py --list

    # 获取数据集信息
    python prepare_data.py --info Movies_and_TV

    # 检查数据集状态
    python prepare_data.py --status Movies_and_TV
"""

import argparse
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from datasets import DatasetManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="数据集准备脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # 数据集参数
    parser.add_argument(
        '--dataset', '-d',
        nargs='+',
        help='数据集名称（可指定多个）'
    )
    
    parser.add_argument(
        '--data-dir',
        type=str,
        default='./data',
        help='数据存储目录 (默认: ./data)'
    )
    
    # 操作选项
    parser.add_argument(
        '--force-download',
        action='store_true',
        help='强制重新下载数据集'
    )
    
    parser.add_argument(
        '--download-only',
        action='store_true',
        help='仅下载，不预处理'
    )
    
    parser.add_argument(
        '--preprocess-only',
        action='store_true',
        help='仅预处理（假设已下载）'
    )
    
    # 预处理参数
    parser.add_argument(
        '--min-interactions',
        type=int,
        default=5,
        help='最小交互次数 (默认: 5)'
    )
    
    parser.add_argument(
        '--max-sequence-length',
        type=int,
        default=128,
        help='最大序列长度 (默认: 128)'
    )
    
    parser.add_argument(
        '--train-ratio',
        type=float,
        default=0.8,
        help='训练集比例 (默认: 0.8)'
    )
    
    parser.add_argument(
        '--val-ratio',
        type=float,
        default=0.1,
        help='验证集比例 (默认: 0.1)'
    )
    
    parser.add_argument(
        '--test-ratio',
        type=float,
        default=0.1,
        help='测试集比例 (默认: 0.1)'
    )
    
    # 信息查询
    info_group = parser.add_mutually_exclusive_group()
    info_group.add_argument(
        '--list', '-l',
        action='store_true',
        help='列出所有可用数据集'
    )
    
    info_group.add_argument(
        '--info', '-i',
        type=str,
        help='获取指定数据集的详细信息'
    )
    
    info_group.add_argument(
        '--status', '-s',
        type=str,
        help='检查指定数据集的状态'
    )
    
    # 其他选项
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def list_datasets(dataset_manager: DatasetManager):
    """列出所有可用数据集"""
    datasets = dataset_manager.list_available_datasets()
    
    print("Available Datasets:")
    print("=" * 50)
    
    for dataset in datasets:
        info = dataset_manager.get_dataset_info(dataset)
        downloaded = "✓" if dataset_manager.is_dataset_downloaded(dataset) else "✗"
        processed = "✓" if dataset_manager.is_dataset_processed(dataset) else "✗"
        
        print(f"  {dataset}")
        print(f"    Description: {info.description}")
        print(f"    Source: {info.source}")
        print(f"    Task Type: {info.task_type}")
        print(f"    Downloaded: {downloaded}")
        print(f"    Processed: {processed}")
        print()


def show_dataset_info(dataset_manager: DatasetManager, dataset_name: str):
    """显示数据集详细信息"""
    info = dataset_manager.get_dataset_info(dataset_name)
    
    if not info:
        print(f"Dataset not found: {dataset_name}")
        return
    
    print(f"Dataset Information: {dataset_name}")
    print("=" * 50)
    print(f"Name: {info.name}")
    print(f"Source: {info.source}")
    print(f"Description: {info.description}")
    print(f"Task Type: {info.task_type}")
    print(f"URL/Path: {info.url_or_path}")
    print(f"Min Interactions: {info.min_interactions}")
    print(f"Max Sequence Length: {info.max_sequence_length}")
    print(f"Train Ratio: {info.train_ratio}")
    print(f"Val Ratio: {info.val_ratio}")
    print(f"Test Ratio: {info.test_ratio}")


def check_dataset_status(dataset_manager: DatasetManager, dataset_name: str):
    """检查数据集状态"""
    if dataset_name not in dataset_manager.list_available_datasets():
        print(f"Dataset not found: {dataset_name}")
        return
    
    downloaded = dataset_manager.is_dataset_downloaded(dataset_name)
    processed = dataset_manager.is_dataset_processed(dataset_name)
    
    print(f"Dataset Status: {dataset_name}")
    print("=" * 30)
    print(f"Downloaded: {'Yes' if downloaded else 'No'}")
    print(f"Processed: {'Yes' if processed else 'No'}")
    
    if downloaded:
        raw_path = dataset_manager.raw_data_dir / dataset_name
        print(f"Raw data path: {raw_path}")
        
        # 显示原始数据文件
        if raw_path.exists():
            files = list(raw_path.iterdir())
            print(f"Raw files: {[f.name for f in files]}")
    
    if processed:
        processed_path = dataset_manager.processed_data_dir / dataset_name
        print(f"Processed data path: {processed_path}")
        
        # 显示处理后的数据文件
        if processed_path.exists():
            files = list(processed_path.iterdir())
            print(f"Processed files: {[f.name for f in files]}")


def prepare_datasets(dataset_manager: DatasetManager, dataset_names: list, args):
    """准备数据集"""
    config_override = {
        'min_interactions': args.min_interactions,
        'max_sequence_length': args.max_sequence_length,
        'train_ratio': args.train_ratio,
        'val_ratio': args.val_ratio,
        'test_ratio': args.test_ratio
    }
    
    success_count = 0
    total_count = len(dataset_names)
    
    for dataset_name in dataset_names:
        print(f"\nPreparing dataset: {dataset_name}")
        print("-" * 40)
        
        try:
            if args.download_only:
                # 仅下载
                success = dataset_manager.download_dataset(dataset_name, args.force_download)
            elif args.preprocess_only:
                # 仅预处理
                success = dataset_manager.preprocess_dataset(dataset_name, config_override)
            else:
                # 完整准备
                success = dataset_manager.prepare_dataset(
                    dataset_name=dataset_name,
                    force_download=args.force_download,
                    config_override=config_override
                )
            
            if success:
                print(f"✓ Successfully prepared dataset: {dataset_name}")
                success_count += 1
            else:
                print(f"✗ Failed to prepare dataset: {dataset_name}")
        
        except Exception as e:
            print(f"✗ Error preparing dataset {dataset_name}: {e}")
            if args.verbose:
                import traceback
                traceback.print_exc()
    
    print(f"\nSummary: {success_count}/{total_count} datasets prepared successfully")
    
    if success_count < total_count:
        sys.exit(1)


def main():
    """主函数"""
    args = parse_args()
    setup_logging(args.verbose)
    
    # 创建数据集管理器
    dataset_manager = DatasetManager(args.data_dir)
    
    try:
        # 处理信息查询命令
        if args.list:
            list_datasets(dataset_manager)
            return
        
        if args.info:
            show_dataset_info(dataset_manager, args.info)
            return
        
        if args.status:
            check_dataset_status(dataset_manager, args.status)
            return
        
        # 检查是否指定了数据集
        if not args.dataset:
            print("Error: Please specify at least one dataset with --dataset")
            print("Use --list to see available datasets")
            sys.exit(1)
        
        # 验证数据集名称
        available_datasets = dataset_manager.list_available_datasets()
        invalid_datasets = [d for d in args.dataset if d not in available_datasets]
        
        if invalid_datasets:
            print(f"Error: Unknown datasets: {invalid_datasets}")
            print(f"Available datasets: {available_datasets}")
            sys.exit(1)
        
        # 准备数据集
        prepare_datasets(dataset_manager, args.dataset, args)
    
    except KeyboardInterrupt:
        logger.info("Data preparation interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        logger.error(f"Data preparation failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
