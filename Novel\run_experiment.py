#!/usr/bin/env python3
"""
LLM-SRec实验运行脚本

支持通过命令行运行各种实验，包括数据准备、模型训练、评估等。

使用示例:
    # 运行完整实验
    python run_experiment.py --config config/collaborative_config.yaml --experiment-name my_experiment

    # 仅准备数据集
    python run_experiment.py --dataset Movies_and_TV --prepare-data-only

    # 仅训练模型
    python run_experiment.py --config experiments/configs/my_experiment.yaml --train-only

    # 仅评估模型
    python run_experiment.py --config experiments/configs/my_experiment.yaml --eval-only --model-path checkpoints/model.pth

    # 列出可用数据集
    python run_experiment.py --list-datasets

    # 获取数据集信息
    python run_experiment.py --dataset-info Movies_and_TV
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from experiments import ExperimentRunner, ConfigManager
from datasets import DatasetManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="LLM-SRec实验运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # 基本参数
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--experiment-name', '-n',
        type=str,
        help='实验名称'
    )
    
    parser.add_argument(
        '--dataset', '-d',
        type=str,
        help='数据集名称'
    )
    
    # 运行模式
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--prepare-data-only',
        action='store_true',
        help='仅准备数据集'
    )
    
    mode_group.add_argument(
        '--train-only',
        action='store_true',
        help='仅训练模型（假设数据已准备）'
    )
    
    mode_group.add_argument(
        '--eval-only',
        action='store_true',
        help='仅评估模型'
    )
    
    # 评估相关参数
    parser.add_argument(
        '--model-path',
        type=str,
        help='模型文件路径（用于评估）'
    )
    
    # 信息查询
    info_group = parser.add_mutually_exclusive_group()
    info_group.add_argument(
        '--list-datasets',
        action='store_true',
        help='列出所有可用数据集'
    )
    
    info_group.add_argument(
        '--dataset-info',
        type=str,
        help='获取指定数据集的信息'
    )
    
    info_group.add_argument(
        '--list-configs',
        action='store_true',
        help='列出所有实验配置'
    )
    
    info_group.add_argument(
        '--experiment-status',
        action='store_true',
        help='获取实验状态'
    )
    
    # 配置覆盖
    parser.add_argument(
        '--batch-size',
        type=int,
        help='批次大小'
    )
    
    parser.add_argument(
        '--learning-rate',
        type=float,
        help='学习率'
    )
    
    parser.add_argument(
        '--num-epochs',
        type=int,
        help='训练轮数'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        help='设备 (cuda:0, cpu等)'
    )
    
    # 其他选项
    parser.add_argument(
        '--force-download',
        action='store_true',
        help='强制重新下载数据集'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def handle_info_commands(args):
    """处理信息查询命令"""
    if args.list_datasets:
        dataset_manager = DatasetManager()
        datasets = dataset_manager.list_available_datasets()
        
        print("Available Datasets:")
        for dataset in datasets:
            info = dataset_manager.get_dataset_info(dataset)
            print(f"  - {dataset}: {info.description}")
        return True
    
    if args.dataset_info:
        dataset_manager = DatasetManager()
        info = dataset_manager.get_dataset_info(args.dataset_info)
        
        if info:
            print(f"Dataset Information: {args.dataset_info}")
            print(f"  Source: {info.source}")
            print(f"  Description: {info.description}")
            print(f"  Task Type: {info.task_type}")
            print(f"  URL/Path: {info.url_or_path}")
        else:
            print(f"Dataset not found: {args.dataset_info}")
        return True
    
    if args.list_configs:
        config_manager = ConfigManager()
        configs = config_manager.list_experiment_configs()
        
        print("Available Experiment Configs:")
        for config in configs:
            print(f"  - {config}")
        return True
    
    return False


def create_experiment_runner(args) -> ExperimentRunner:
    """创建实验运行器"""
    config_path = args.config
    experiment_name = args.experiment_name
    
    # 如果指定了数据集但没有配置文件，创建数据集特定的配置
    if args.dataset and not config_path:
        config_manager = ConfigManager()
        if not experiment_name:
            experiment_name = f"experiment_{args.dataset}"
        
        config_path = config_manager.create_dataset_experiment(args.dataset, experiment_name)
        logger.info(f"Created dataset-specific config: {config_path}")
    
    # 创建实验运行器
    runner = ExperimentRunner(config_path, experiment_name)
    
    # 应用命令行参数覆盖
    if args.batch_size:
        runner.config['training']['batch_size'] = args.batch_size
    
    if args.learning_rate:
        runner.config['training']['learning_rate'] = args.learning_rate
    
    if args.num_epochs:
        runner.config['training']['num_epochs'] = args.num_epochs
    
    if args.device:
        runner.config['device'] = args.device
    
    return runner


def main():
    """主函数"""
    args = parse_args()
    setup_logging(args.verbose)
    
    try:
        # 处理信息查询命令
        if handle_info_commands(args):
            return
        
        # 创建实验运行器
        runner = create_experiment_runner(args)
        
        # 处理实验状态查询
        if args.experiment_status:
            status = runner.get_experiment_status()
            print("Experiment Status:")
            print(f"  Name: {status['experiment_name']}")
            print(f"  Dataset: {status['dataset']}")
            print(f"  Dataset Downloaded: {status['dataset_downloaded']}")
            print(f"  Dataset Processed: {status['dataset_processed']}")
            print(f"  Experiment Dir: {status['experiment_dir']}")
            return
        
        # 执行相应的操作
        if args.prepare_data_only:
            logger.info("Running data preparation only...")
            success = runner.run_data_preparation_only()
            if success:
                print("Data preparation completed successfully!")
            else:
                print("Data preparation failed!")
                sys.exit(1)
        
        elif args.train_only:
            logger.info("Running training only...")
            results = runner.run_training_only()
            print("Training completed successfully!")
            print(f"Final training loss: {results.get('final_loss', 'N/A')}")
        
        elif args.eval_only:
            if not args.model_path:
                print("Error: --model-path is required for evaluation only mode")
                sys.exit(1)
            
            logger.info("Running evaluation only...")
            results = runner.run_evaluation_only(args.model_path)
            print("Evaluation completed successfully!")
            
            # 打印评估结果
            print("\nEvaluation Results:")
            for metric, value in results.items():
                if isinstance(value, (int, float)):
                    print(f"  {metric}: {value:.4f}")
        
        else:
            # 运行完整实验
            logger.info("Running full experiment...")
            results = runner.run_full_experiment()
            print("Experiment completed successfully!")
            
            # 打印结果摘要
            if 'evaluation_results' in results:
                print("\nFinal Evaluation Results:")
                for metric, value in results['evaluation_results'].items():
                    if isinstance(value, (int, float)):
                        print(f"  {metric}: {value:.4f}")
            
            print(f"\nExperiment time: {results.get('experiment_time', 'N/A'):.2f} seconds")
            print(f"Results saved to: {runner.experiment_dir}")
    
    except KeyboardInterrupt:
        logger.info("Experiment interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
