"""
数据预处理器

负责将原始数据集转换为模型训练所需的格式。
"""

import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, Counter
import pickle

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self):
        self.user_encoder = {}
        self.item_encoder = {}
        self.user_decoder = {}
        self.item_decoder = {}
    
    def preprocess(self, raw_data_path: str, output_path: str, config) -> bool:
        """
        预处理数据集
        
        Args:
            raw_data_path: 原始数据路径
            output_path: 输出路径
            config: 数据集配置
            
        Returns:
            是否预处理成功
        """
        try:
            logger.info(f"Preprocessing dataset: {config.name}")
            
            # 根据数据集类型选择预处理方法
            if config.source == 'amazon':
                return self._preprocess_amazon_data(raw_data_path, output_path, config)
            elif config.source == 'huggingface':
                return self._preprocess_huggingface_data(raw_data_path, output_path, config)
            else:
                logger.error(f"Unsupported data source: {config.source}")
                return False
                
        except Exception as e:
            logger.error(f"Error preprocessing dataset: {e}")
            return False
    
    def _preprocess_amazon_data(self, raw_data_path: str, output_path: str, config) -> bool:
        """预处理Amazon数据集"""
        try:
            raw_path = Path(raw_data_path)
            output_path = Path(output_path)

            # 查找JSON文件（包括可能的压缩文件）
            json_files = list(raw_path.glob("*.json"))
            gz_files = list(raw_path.glob("*.json.gz"))

            # Amazon数据集的特殊命名模式
            amazon_gz_files = list(raw_path.glob("reviews_*.json.gz"))
            amazon_json_files = list(raw_path.glob("reviews_*.json"))

            # 合并所有可能的文件
            all_json_files = json_files + amazon_json_files
            all_gz_files = gz_files + amazon_gz_files

            if not all_json_files and not all_gz_files:
                logger.error(f"No JSON or JSON.gz files found in {raw_data_path}")
                logger.info(f"Directory exists: {raw_path.exists()}")
                logger.info(f"Directory contents: {list(raw_path.glob('*')) if raw_path.exists() else 'Directory does not exist'}")
                logger.info(f"All files in directory: {list(raw_path.rglob('*')) if raw_path.exists() else 'Directory does not exist'}")
                return False

            # 优先使用解压后的JSON文件，否则使用压缩文件
            if all_json_files:
                data_file = all_json_files[0]
                logger.info(f"Processing JSON file: {data_file}")
                file_opener = open
                open_mode = 'r'
            else:
                data_file = all_gz_files[0]
                logger.info(f"Processing compressed file: {data_file}")
                import gzip
                file_opener = gzip.open
                open_mode = 'rt'

            # 读取数据
            interactions = []
            total_lines = 0
            valid_lines = 0

            with file_opener(data_file, open_mode, encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    total_lines += 1
                    try:
                        item = json.loads(line.strip())

                        # 检查必要字段
                        if 'reviewerID' in item and 'asin' in item:
                            interactions.append({
                                'user_id': item.get('reviewerID', ''),
                                'item_id': item.get('asin', ''),
                                'rating': float(item.get('overall', 5.0)),  # 默认评分5.0
                                'timestamp': int(item.get('unixReviewTime', 0))
                            })
                            valid_lines += 1

                        # 每10万行显示一次进度
                        if total_lines % 100000 == 0:
                            logger.info(f"Processed {total_lines} lines, valid: {valid_lines}")

                    except json.JSONDecodeError:
                        if line_num < 10:  # 只记录前几个错误
                            logger.warning(f"Skipping invalid JSON at line {line_num + 1}")
                        continue
                    except (KeyError, ValueError) as e:
                        if line_num < 10:
                            logger.warning(f"Skipping invalid data at line {line_num + 1}: {e}")
                        continue

            logger.info(f"Loaded {len(interactions)} valid interactions from {total_lines} total lines")
            
            # 转换为DataFrame
            df = pd.DataFrame(interactions)
            
            # 过滤数据
            df = self._filter_interactions(df, config.min_interactions)
            
            # 编码用户和物品ID
            df = self._encode_ids(df)
            
            # 按时间排序
            df = df.sort_values(['user_id', 'timestamp'])
            
            # 创建序列数据
            sequences = self._create_sequences(df, config.max_sequence_length)
            
            # 分割数据
            train_data, val_data, test_data = self._split_data(
                sequences, config.train_ratio, config.val_ratio, config.test_ratio
            )
            
            # 保存数据
            self._save_processed_data(output_path, train_data, val_data, test_data)
            
            # 保存编码器
            self._save_encoders(output_path)
            
            # 保存统计信息
            self._save_statistics(output_path, df, sequences)
            
            logger.info(f"Successfully preprocessed Amazon dataset: {config.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error preprocessing Amazon data: {e}")
            return False
    
    def _preprocess_huggingface_data(self, raw_data_path: str, output_path: str, config) -> bool:
        """预处理HuggingFace数据集"""
        try:
            raw_path = Path(raw_data_path)
            output_path = Path(output_path)
            
            # 查找JSON文件
            json_files = list(raw_path.glob("*.json"))
            if not json_files:
                logger.error(f"No JSON files found in {raw_data_path}")
                return False
            
            # 合并所有分割的数据
            all_interactions = []
            for json_file in json_files:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_interactions.extend(data)
                    else:
                        logger.warning(f"Unexpected data format in {json_file}")
            
            logger.info(f"Loaded {len(all_interactions)} interactions from HuggingFace dataset")
            
            # 转换为标准格式
            interactions = []
            for item in all_interactions:
                interactions.append({
                    'user_id': str(item.get('user_id', item.get('userId', ''))),
                    'item_id': str(item.get('item_id', item.get('movieId', item.get('itemId', '')))),
                    'rating': float(item.get('rating', item.get('score', 0))),
                    'timestamp': int(item.get('timestamp', item.get('time', 0)))
                })
            
            # 转换为DataFrame并处理
            df = pd.DataFrame(interactions)
            df = self._filter_interactions(df, config.min_interactions)
            df = self._encode_ids(df)
            df = df.sort_values(['user_id', 'timestamp'])
            
            sequences = self._create_sequences(df, config.max_sequence_length)
            train_data, val_data, test_data = self._split_data(
                sequences, config.train_ratio, config.val_ratio, config.test_ratio
            )
            
            self._save_processed_data(output_path, train_data, val_data, test_data)
            self._save_encoders(output_path)
            self._save_statistics(output_path, df, sequences)
            
            logger.info(f"Successfully preprocessed HuggingFace dataset: {config.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error preprocessing HuggingFace data: {e}")
            return False
    
    def _filter_interactions(self, df: pd.DataFrame, min_interactions: int) -> pd.DataFrame:
        """过滤交互数据"""
        logger.info("Filtering interactions...")
        
        # 移除空值
        df = df.dropna(subset=['user_id', 'item_id'])
        
        # 过滤用户（至少有min_interactions个交互）
        user_counts = df['user_id'].value_counts()
        valid_users = user_counts[user_counts >= min_interactions].index
        df = df[df['user_id'].isin(valid_users)]
        
        # 过滤物品（至少有min_interactions个交互）
        item_counts = df['item_id'].value_counts()
        valid_items = item_counts[item_counts >= min_interactions].index
        df = df[df['item_id'].isin(valid_items)]
        
        logger.info(f"After filtering: {len(df)} interactions, {df['user_id'].nunique()} users, {df['item_id'].nunique()} items")
        return df
    
    def _encode_ids(self, df: pd.DataFrame) -> pd.DataFrame:
        """编码用户和物品ID"""
        logger.info("Encoding user and item IDs...")
        
        # 编码用户ID
        unique_users = df['user_id'].unique()
        self.user_encoder = {user: idx for idx, user in enumerate(unique_users)}
        self.user_decoder = {idx: user for user, idx in self.user_encoder.items()}
        
        # 编码物品ID
        unique_items = df['item_id'].unique()
        self.item_encoder = {item: idx for idx, item in enumerate(unique_items)}
        self.item_decoder = {idx: item for item, idx in self.item_encoder.items()}
        
        # 应用编码
        df['user_id'] = df['user_id'].map(self.user_encoder)
        df['item_id'] = df['item_id'].map(self.item_encoder)
        
        logger.info(f"Encoded {len(self.user_encoder)} users and {len(self.item_encoder)} items")
        return df
    
    def _create_sequences(self, df: pd.DataFrame, max_length: int) -> List[Dict]:
        """创建序列数据"""
        logger.info("Creating sequences...")
        
        sequences = []
        for user_id, group in df.groupby('user_id'):
            items = group['item_id'].tolist()
            ratings = group['rating'].tolist()
            timestamps = group['timestamp'].tolist()
            
            # 创建滑动窗口序列
            for i in range(1, len(items)):
                seq_items = items[:i]
                seq_ratings = ratings[:i]
                target_item = items[i]
                target_rating = ratings[i]
                
                # 截断序列
                if len(seq_items) > max_length:
                    seq_items = seq_items[-max_length:]
                    seq_ratings = seq_ratings[-max_length:]
                
                sequences.append({
                    'user_id': user_id,
                    'sequence': seq_items,
                    'ratings': seq_ratings,
                    'target_item': target_item,
                    'target_rating': target_rating,
                    'sequence_length': len(seq_items)
                })
        
        logger.info(f"Created {len(sequences)} sequences")
        return sequences
    
    def _split_data(self, sequences: List[Dict], train_ratio: float, 
                   val_ratio: float, test_ratio: float) -> Tuple[List, List, List]:
        """分割数据"""
        logger.info("Splitting data...")
        
        # 按用户分割以避免数据泄露
        user_sequences = defaultdict(list)
        for seq in sequences:
            user_sequences[seq['user_id']].append(seq)
        
        train_data, val_data, test_data = [], [], []
        
        for user_id, user_seqs in user_sequences.items():
            n_seqs = len(user_seqs)
            n_train = int(n_seqs * train_ratio)
            n_val = int(n_seqs * val_ratio)
            
            train_data.extend(user_seqs[:n_train])
            val_data.extend(user_seqs[n_train:n_train + n_val])
            test_data.extend(user_seqs[n_train + n_val:])
        
        logger.info(f"Split data: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test")
        return train_data, val_data, test_data
    
    def _save_processed_data(self, output_path: Path, train_data: List, 
                           val_data: List, test_data: List):
        """保存处理后的数据"""
        output_path.mkdir(parents=True, exist_ok=True)
        
        with open(output_path / 'train.json', 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2)
        
        with open(output_path / 'val.json', 'w', encoding='utf-8') as f:
            json.dump(val_data, f, indent=2)
        
        with open(output_path / 'test.json', 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2)
        
        logger.info(f"Saved processed data to {output_path}")
    
    def _save_encoders(self, output_path: Path):
        """保存编码器"""
        encoders = {
            'user_encoder': self.user_encoder,
            'item_encoder': self.item_encoder,
            'user_decoder': self.user_decoder,
            'item_decoder': self.item_decoder
        }
        
        with open(output_path / 'encoders.pkl', 'wb') as f:
            pickle.dump(encoders, f)
        
        logger.info(f"Saved encoders to {output_path / 'encoders.pkl'}")
    
    def _save_statistics(self, output_path: Path, df: pd.DataFrame, sequences: List[Dict]):
        """保存统计信息"""
        stats = {
            'num_users': df['user_id'].nunique(),
            'num_items': df['item_id'].nunique(),
            'num_interactions': len(df),
            'num_sequences': len(sequences),
            'avg_sequence_length': np.mean([seq['sequence_length'] for seq in sequences]),
            'rating_distribution': df['rating'].value_counts().to_dict()
        }
        
        with open(output_path / 'statistics.json', 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2)
        
        logger.info(f"Saved statistics to {output_path / 'statistics.json'}")
        logger.info(f"Dataset statistics: {stats}")
