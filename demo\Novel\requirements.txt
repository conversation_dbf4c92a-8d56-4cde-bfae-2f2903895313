# ==================== 核心深度学习框架 ====================
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
datasets>=2.12.0

# ==================== 大语言模型和微调 ====================
peft>=0.4.0
accelerate>=0.20.0
bitsandbytes>=0.39.0
sentencepiece>=0.1.99

# ==================== 数据处理和科学计算 ====================
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# ==================== 推荐系统专用库 ====================
sentence-transformers>=2.2.0
faiss-cpu>=1.7.4

# ==================== 实验跟踪和可视化 ====================
tensorboard>=2.13.0
wandb>=0.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# ==================== 端云协同和通信 ====================
aiohttp>=3.8.0
websockets>=10.0
requests>=2.31.0
grpcio>=1.56.0
protobuf>=4.23.0

# ==================== 隐私保护和安全 ====================
cryptography>=41.0.0
pycryptodome>=3.18.0

# ==================== 配置和工具 ====================
pyyaml>=6.0
omegaconf>=2.3.0
hydra-core>=1.3.0
tqdm>=4.65.0
psutil>=5.9.0

# ==================== 开发和测试 ====================
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

# ==================== 可选依赖 ====================
# Jupyter notebook支持
jupyter>=1.0.0
ipywidgets>=8.0.0

# 分布式训练支持
deepspeed>=0.9.0

# 模型压缩
torch-pruning>=1.2.0
