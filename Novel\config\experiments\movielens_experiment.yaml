# MovieLens数据集实验配置
# 针对电影推荐的优化配置

experiment_name: "movielens_experiment"
seed: 42
device: "cuda:0"
log_level: "INFO"

# ==================== 数据配置 ====================
data:
  dataset: "ml-1m"
  data_dir: "./data"
  raw_data_dir: "./data/raw"
  processed_data_dir: "./data/processed"
  
  # MovieLens数据集特定参数
  min_interactions: 10  # MovieLens用户交互较多
  max_sequence_length: 100
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 数据增强
  data_augmentation: true
  augmentation_ratio: 0.1

# ==================== 小模型配置 ====================
small_model:
  model_type: "cf_srec"
  
  # 针对MovieLens的模型架构
  item_num: 4000  # MovieLens电影数量
  hidden_units: 64
  num_blocks: 3  # 增加模型深度
  num_heads: 2
  dropout_rate: 0.2
  max_sequence_length: 100
  
  # LoRA配置
  use_lora: true
  lora_r: 8
  lora_alpha: 16
  lora_dropout: 0.1

# ==================== 大模型配置 ====================
large_model:
  model_type: "llm_recommender"
  
  # 使用中等大小的LLM
  llm_model: "llama-7b"
  load_in_8bit: true
  load_in_4bit: false
  
  # LLM生成参数
  max_length: 512
  temperature: 1.0
  top_p: 0.9
  top_k: 50
  
  # LoRA微调配置
  use_lora: true
  lora_r: 16
  lora_alpha: 32
  lora_dropout: 0.1
  lora_target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]

# ==================== 协同机制配置 ====================
collaboration:
  collaboration_method: "joint_training"  # 使用联合训练
  
  # 知识蒸馏参数
  distillation_temperature: 4.0
  distillation_weight: 0.2
  
  # 特征对齐参数
  alignment_weight: 0.1
  alignment_method: "mse"
  
  # 联合训练参数
  joint_training: true
  small_model_weight: 0.3
  large_model_weight: 0.4
  collaborative_weight: 0.3

# ==================== 训练配置 ====================
training:
  # 基础训练参数
  num_epochs: 50
  batch_size: 32
  learning_rate: 1e-4
  weight_decay: 1e-5
  gradient_clip: 1.0
  
  # 学习率调度
  scheduler: "linear"
  warmup_steps: 1000
  warmup_ratio: 0.1
  
  # 早停参数
  patience: 10
  min_delta: 1e-4
  
  # 输出配置
  output_dir: "./checkpoints/movielens"
  save_steps: 1000
  eval_steps: 500
  logging_steps: 100
  
  # 实验跟踪
  use_wandb: false
  wandb_project: "llm-srec-movielens"
  use_tensorboard: true

# ==================== 评估配置 ====================
evaluation:
  # 评估指标
  metrics: ["ndcg@10", "ndcg@20", "hit_rate@10", "hit_rate@20", "recall@10", "recall@20", "precision@10", "mrr@10"]
  
  # 推荐参数
  top_k_list: [5, 10, 20, 50]
  
  # 评估频率
  eval_during_training: true
  eval_steps: 500

# ==================== 推理配置 ====================
inference:
  batch_size: 64
  top_k: 20  # 电影推荐通常需要更多选择
  output_format: "json"
  include_scores: true
  include_explanations: true

# ==================== 优化配置 ====================
optimization:
  # 混合精度训练
  fp16: true
  bf16: false
  
  # 梯度累积
  gradient_accumulation_steps: 2  # 增加梯度累积
  
  # 数据并行
  dataloader_num_workers: 4
  dataloader_pin_memory: true
  
  # 模型并行
  model_parallel: false
  device_map: "auto"

# ==================== 损失函数配置 ====================
loss:
  recommendation_loss: "cross_entropy"
  
  # 辅助损失权重
  small_model_loss_weight: 0.3
  large_model_loss_weight: 0.4
  collaborative_loss_weight: 0.3
  distillation_loss_weight: 0.2
  alignment_loss_weight: 0.1
  
  # 正则化
  l1_regularization: 0.0
  l2_regularization: 1e-5

# ==================== 数据增强配置 ====================
data_augmentation:
  # 序列增强
  sequence_augmentation: true
  crop_ratio: 0.2
  mask_ratio: 0.1
  reorder_ratio: 0.1
  
  # 负采样
  negative_sampling: true
  negative_sample_ratio: 1.0
  negative_sampling_strategy: "random"

# ==================== 模型压缩配置 ====================
compression:
  # 量化
  quantization: false
  quantization_bits: 8
  
  # 剪枝
  pruning: false
  pruning_ratio: 0.1
  
  # 蒸馏
  model_distillation: false
  teacher_model_path: ""

# ==================== 实验配置 ====================
experiment:
  # 消融实验
  ablation_study: true  # 启用消融实验
  ablation_components: ["small_model", "large_model", "distillation", "alignment"]
  
  # 超参数搜索
  hyperparameter_search: false
  search_space:
    learning_rate: [5e-5, 1e-4, 2e-4]
    batch_size: [16, 32, 64]
    hidden_units: [32, 64, 128]
    num_blocks: [2, 3, 4]
    distillation_temperature: [2.0, 4.0, 6.0]
  
  # 多种子实验
  multi_seed: true  # 启用多种子实验
  seeds: [42, 123, 456, 789, 999]
