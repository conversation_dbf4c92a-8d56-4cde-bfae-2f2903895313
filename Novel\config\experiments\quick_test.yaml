# 快速测试配置
# 用于快速验证系统功能的轻量级配置

experiment_name: "quick_test"
seed: 42
device: "cpu"  # 使用CPU进行快速测试
log_level: "INFO"

# ==================== 数据配置 ====================
data:
  dataset: "ml-100k"  # 使用较小的数据集
  data_dir: "./data"
  raw_data_dir: "./data/raw"
  processed_data_dir: "./data/processed"
  
  # 快速测试参数
  min_interactions: 3  # 降低最小交互次数
  max_sequence_length: 20  # 减少序列长度
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 禁用数据增强以加快速度
  data_augmentation: false
  augmentation_ratio: 0.0

# ==================== 小模型配置 ====================
small_model:
  model_type: "cf_srec"
  
  # 最小模型配置
  item_num: 1000
  hidden_units: 16  # 最小隐藏单元
  num_blocks: 1  # 单层
  num_heads: 1
  dropout_rate: 0.1
  max_sequence_length: 20
  
  # 禁用LoRA
  use_lora: false
  lora_r: 4
  lora_alpha: 8
  lora_dropout: 0.1

# ==================== 大模型配置 ====================
large_model:
  model_type: "llm_recommender"
  
  # 使用最小的LLM配置
  llm_model: "llama-3b"
  load_in_8bit: false
  load_in_4bit: true  # 使用4bit量化
  
  # 最小生成参数
  max_length: 64
  temperature: 1.0
  top_p: 0.9
  top_k: 20
  
  # 最小LoRA配置
  use_lora: true
  lora_r: 4
  lora_alpha: 8
  lora_dropout: 0.1
  lora_target_modules: ["q_proj", "v_proj"]

# ==================== 协同机制配置 ====================
collaboration:
  collaboration_method: "knowledge_distillation"  # 简单的蒸馏
  
  # 简化的蒸馏参数
  distillation_temperature: 2.0
  distillation_weight: 0.5
  
  # 禁用特征对齐
  alignment_weight: 0.0
  alignment_method: "mse"
  
  # 简化的联合训练
  joint_training: false
  small_model_weight: 0.5
  large_model_weight: 0.5
  collaborative_weight: 0.0

# ==================== 训练配置 ====================
training:
  # 快速训练参数
  num_epochs: 3  # 只训练3轮
  batch_size: 16  # 小批次
  learning_rate: 1e-3  # 较大学习率
  weight_decay: 0.0
  gradient_clip: 1.0
  
  # 简单调度器
  scheduler: "step"
  warmup_steps: 10
  warmup_ratio: 0.1
  
  # 禁用早停
  patience: 100
  min_delta: 0.0
  
  # 输出配置
  output_dir: "./checkpoints/quick_test"
  save_steps: 100
  eval_steps: 50
  logging_steps: 10
  
  # 禁用实验跟踪
  use_wandb: false
  wandb_project: "llm-srec-test"
  use_tensorboard: false

# ==================== 评估配置 ====================
evaluation:
  # 基本评估指标
  metrics: ["ndcg@5", "hit_rate@5", "recall@5"]
  
  # 推荐参数
  top_k_list: [5, 10]
  
  # 评估频率
  eval_during_training: true
  eval_steps: 50

# ==================== 推理配置 ====================
inference:
  batch_size: 32
  top_k: 5
  output_format: "json"
  include_scores: false
  include_explanations: false

# ==================== 优化配置 ====================
optimization:
  # 禁用混合精度
  fp16: false
  bf16: false
  
  # 无梯度累积
  gradient_accumulation_steps: 1
  
  # 最小数据并行
  dataloader_num_workers: 0
  dataloader_pin_memory: false
  
  # 无模型并行
  model_parallel: false
  device_map: "auto"

# ==================== 损失函数配置 ====================
loss:
  recommendation_loss: "cross_entropy"
  
  # 简化的损失权重
  small_model_loss_weight: 0.5
  large_model_loss_weight: 0.5
  collaborative_loss_weight: 0.0
  distillation_loss_weight: 0.5
  alignment_loss_weight: 0.0
  
  # 无正则化
  l1_regularization: 0.0
  l2_regularization: 0.0

# ==================== 数据增强配置 ====================
data_augmentation:
  # 禁用所有数据增强
  sequence_augmentation: false
  crop_ratio: 0.0
  mask_ratio: 0.0
  reorder_ratio: 0.0
  
  # 简单负采样
  negative_sampling: true
  negative_sample_ratio: 0.5
  negative_sampling_strategy: "random"

# ==================== 实验配置 ====================
experiment:
  # 禁用所有高级实验功能
  ablation_study: false
  ablation_components: []
  
  # 禁用超参数搜索
  hyperparameter_search: false
  search_space: {}
  
  # 单种子
  multi_seed: false
  seeds: [42]
