"""
Novel: Sequential Knowledge Enhanced Large-Small Model Recommendation via Edge-Cloud Collaboration

端云协同推荐系统主入口
实现端侧轻量化推理和云端深度优化的协同机制

Author: Novel Team
Date: 2024
"""

import torch
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time

from models.client.cf_srec_client import CFSRecClient
from models.cloud.llm_cloud_server import LLMCloudServer
from models.knowledge_distillation.distillation_engine import DistillationEngine
from utils.communication import EdgeCloudCommunicator
from utils.data_utils import load_config


@dataclass
class SystemMetrics:
    """系统性能指标"""
    edge_inference_time: float = 0.0
    cloud_inference_time: float = 0.0
    communication_time: float = 0.0
    total_response_time: float = 0.0
    recommendation_accuracy: float = 0.0
    privacy_protection_level: float = 1.0


class EdgeCloudRecommendationSystem:
    """
    Novel端云协同推荐系统
    
    核心功能：
    1. 端侧轻量化序列建模和快速响应
    2. 云端LLM深度推荐优化
    3. 智能知识蒸馏和模型更新
    4. 隐私保护的端云通信
    """
    
    def __init__(self, config_path: str):
        """
        初始化端云协同系统
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.logger = self._setup_logger()
        
        # 初始化各层组件
        self._init_edge_layer()
        self._init_cloud_layer()
        self._init_distillation_layer()
        self._init_communication()
        
        # 系统状态
        self.is_initialized = True
        self.metrics = SystemMetrics()
        
        self.logger.info("Novel端云协同推荐系统初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('Novel')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_edge_layer(self):
        """初始化端侧层"""
        edge_config = self.config.get('edge', {})
        self.edge_model = CFSRecClient(edge_config)
        self.logger.info("端侧CF-SRec模型初始化完成")
    
    def _init_cloud_layer(self):
        """初始化云端层"""
        cloud_config = self.config.get('cloud', {})
        self.cloud_model = LLMCloudServer(cloud_config)
        self.logger.info("云端LLM服务器初始化完成")
    
    def _init_distillation_layer(self):
        """初始化知识蒸馏层"""
        distillation_config = self.config.get('distillation', {})
        self.distillation_engine = DistillationEngine(distillation_config)
        self.logger.info("知识蒸馏引擎初始化完成")
    
    def _init_communication(self):
        """初始化端云通信"""
        comm_config = self.config.get('communication', {})
        self.communicator = EdgeCloudCommunicator(comm_config)
        self.logger.info("端云通信模块初始化完成")
    
    async def recommend(
        self, 
        user_id: str, 
        user_sequence: List[int], 
        top_k: int = 10
    ) -> Dict[str, Any]:
        """
        端云协同推荐主流程
        
        Args:
            user_id: 用户ID
            user_sequence: 用户交互序列
            top_k: 推荐物品数量
            
        Returns:
            推荐结果字典，包含推荐列表、分数、解释等
        """
        start_time = time.time()
        
        try:
            # 1. 端侧处理：序列建模和用户表示生成
            edge_start = time.time()
            user_representation = await self._edge_inference(user_sequence)
            self.metrics.edge_inference_time = time.time() - edge_start
            
            # 2. 端云通信：传输用户表示
            comm_start = time.time()
            cloud_input = await self.communicator.send_to_cloud(
                user_representation, user_id
            )
            self.metrics.communication_time = time.time() - comm_start
            
            # 3. 云端处理：LLM推荐优化
            cloud_start = time.time()
            recommendations = await self._cloud_inference(cloud_input, top_k)
            self.metrics.cloud_inference_time = time.time() - cloud_start
            
            # 4. 异步知识蒸馏（不阻塞推荐响应）
            asyncio.create_task(self._async_knowledge_distillation(
                user_representation, recommendations
            ))
            
            # 5. 构建返回结果
            result = self._build_recommendation_result(
                recommendations, user_id, user_sequence
            )
            
            self.metrics.total_response_time = time.time() - start_time
            
            self.logger.info(
                f"用户 {user_id} 推荐完成，总耗时: {self.metrics.total_response_time:.3f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"推荐过程出错: {str(e)}")
            return self._build_error_result(str(e))
    
    async def _edge_inference(self, user_sequence: List[int]) -> torch.Tensor:
        """
        端侧推理：生成用户表示
        
        Args:
            user_sequence: 用户交互序列
            
        Returns:
            64维用户表示μ
        """
        # 转换为tensor
        sequence_tensor = torch.tensor(user_sequence, dtype=torch.long)
        
        # CF-SRec序列建模
        with torch.no_grad():
            user_mu = self.edge_model.generate_user_representation(sequence_tensor)
        
        # 隐私保护处理（可选添加噪声）
        if self.config.get('privacy', {}).get('add_noise', False):
            noise_level = self.config['privacy']['noise_level']
            noise = torch.randn_like(user_mu) * noise_level
            user_mu = user_mu + noise
        
        return user_mu
    
    async def _cloud_inference(
        self, 
        cloud_input: Dict[str, Any], 
        top_k: int
    ) -> Dict[str, Any]:
        """
        云端推理：LLM推荐优化
        
        Args:
            cloud_input: 云端输入数据
            top_k: 推荐数量
            
        Returns:
            推荐结果
        """
        user_mu = cloud_input['user_representation']
        user_id = cloud_input['user_id']
        
        # LLM推荐优化
        recommendations = await self.cloud_model.generate_recommendations(
            user_mu, user_id, top_k
        )
        
        return recommendations
    
    async def _async_knowledge_distillation(
        self, 
        user_representation: torch.Tensor,
        cloud_recommendations: Dict[str, Any]
    ):
        """
        异步知识蒸馏：更新端侧模型
        
        Args:
            user_representation: 端侧用户表示
            cloud_recommendations: 云端推荐结果
        """
        try:
            # 提取云端知识
            teacher_knowledge = await self.distillation_engine.extract_knowledge(
                cloud_recommendations
            )
            
            # 计算蒸馏损失并更新端侧模型
            await self.distillation_engine.update_edge_model(
                self.edge_model, user_representation, teacher_knowledge
            )
            
            self.logger.debug("知识蒸馏更新完成")
            
        except Exception as e:
            self.logger.warning(f"知识蒸馏过程出错: {str(e)}")
    
    def _build_recommendation_result(
        self, 
        recommendations: Dict[str, Any],
        user_id: str,
        user_sequence: List[int]
    ) -> Dict[str, Any]:
        """构建推荐结果"""
        return {
            'user_id': user_id,
            'recommendations': recommendations.get('item_ids', []),
            'scores': recommendations.get('scores', []),
            'explanations': recommendations.get('explanations', []),
            'metrics': {
                'edge_time': self.metrics.edge_inference_time,
                'cloud_time': self.metrics.cloud_inference_time,
                'comm_time': self.metrics.communication_time,
                'total_time': self.metrics.total_response_time
            },
            'privacy_info': {
                'data_transmitted': '64-dim user representation only',
                'original_data_location': 'edge device',
                'privacy_level': 'high'
            }
        }
    
    def _build_error_result(self, error_msg: str) -> Dict[str, Any]:
        """构建错误结果"""
        return {
            'error': True,
            'message': error_msg,
            'recommendations': [],
            'scores': [],
            'timestamp': time.time()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'initialized': self.is_initialized,
            'edge_model_status': 'ready' if hasattr(self, 'edge_model') else 'not_ready',
            'cloud_model_status': 'ready' if hasattr(self, 'cloud_model') else 'not_ready',
            'distillation_status': 'ready' if hasattr(self, 'distillation_engine') else 'not_ready',
            'last_metrics': {
                'edge_time': self.metrics.edge_inference_time,
                'cloud_time': self.metrics.cloud_inference_time,
                'total_time': self.metrics.total_response_time
            }
        }
    
    async def shutdown(self):
        """优雅关闭系统"""
        self.logger.info("正在关闭Novel端云协同系统...")
        
        # 关闭各组件
        if hasattr(self, 'communicator'):
            await self.communicator.close()
        
        if hasattr(self, 'cloud_model'):
            await self.cloud_model.shutdown()
        
        self.logger.info("系统关闭完成")


# 使用示例
async def main():
    """主函数示例"""
    # 初始化系统
    system = EdgeCloudRecommendationSystem('config/collaborative_config.yaml')
    
    # 模拟用户请求
    user_sequence = [120, 135, 142, 156, 167, 178, 189, 195]
    
    # 获取推荐
    result = await system.recommend(
        user_id="user_001",
        user_sequence=user_sequence,
        top_k=10
    )
    
    print("推荐结果:", result)
    
    # 关闭系统
    await system.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
