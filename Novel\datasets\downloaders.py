"""
数据集下载器

支持从不同数据源下载数据集，包括HuggingFace、Amazon等。
"""

import os
import gzip
import json
import logging
import requests
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional
from urllib.parse import urlparse

try:
    from datasets import load_dataset
    HF_AVAILABLE = True
except ImportError:
    HF_AVAILABLE = False
    logging.warning("HuggingFace datasets not available. Install with: pip install datasets")

logger = logging.getLogger(__name__)


class BaseDownloader(ABC):
    """数据下载器基类"""
    
    @abstractmethod
    def download(self, source: str, output_dir: str) -> bool:
        """
        下载数据集
        
        Args:
            source: 数据源（URL或标识符）
            output_dir: 输出目录
            
        Returns:
            是否下载成功
        """
        pass


class HuggingFaceDownloader(BaseDownloader):
    """HuggingFace数据集下载器"""
    
    def download(self, dataset_name: str, output_dir: str) -> bool:
        """
        从HuggingFace下载数据集
        
        Args:
            dataset_name: HuggingFace数据集名称
            output_dir: 输出目录
            
        Returns:
            是否下载成功
        """
        if not HF_AVAILABLE:
            logger.error("HuggingFace datasets not available")
            return False
        
        try:
            logger.info(f"Downloading HuggingFace dataset: {dataset_name}")
            
            # 下载数据集
            dataset = load_dataset(dataset_name)
            
            # 保存到本地
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 保存各个分割
            for split_name, split_data in dataset.items():
                split_file = output_path / f"{split_name}.json"
                
                # 转换为JSON格式保存
                data_list = []
                for item in split_data:
                    data_list.append(item)
                
                with open(split_file, 'w', encoding='utf-8') as f:
                    json.dump(data_list, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Saved {split_name} split to {split_file}")
            
            # 保存数据集信息
            info_file = output_path / "dataset_info.json"
            dataset_info = {
                'name': dataset_name,
                'source': 'huggingface',
                'splits': list(dataset.keys()),
                'features': str(dataset[list(dataset.keys())[0]].features) if dataset else None
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Successfully downloaded HuggingFace dataset: {dataset_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading HuggingFace dataset {dataset_name}: {e}")
            return False


class AmazonDownloader(BaseDownloader):
    """Amazon数据集下载器"""
    
    def download(self, url: str, output_dir: str) -> bool:
        """
        从Amazon下载数据集
        
        Args:
            url: 数据集URL
            output_dir: 输出目录
            
        Returns:
            是否下载成功
        """
        try:
            logger.info(f"Downloading Amazon dataset from: {url}")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 从URL获取文件名
            parsed_url = urlparse(url)
            filename = Path(parsed_url.path).name
            
            if not filename:
                filename = "dataset.json.gz"
            
            file_path = output_path / filename
            
            # 下载文件
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 简单的进度显示
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            if downloaded_size % (1024 * 1024) == 0:  # 每MB显示一次
                                logger.info(f"Download progress: {progress:.1f}%")
            
            logger.info(f"Downloaded file: {file_path}")
            
            # 如果是gzip文件，解压缩
            if filename.endswith('.gz'):
                self._extract_gzip(file_path, output_path)
            
            logger.info(f"Successfully downloaded Amazon dataset from: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading Amazon dataset from {url}: {e}")
            return False
    
    def _extract_gzip(self, gzip_path: Path, output_dir: Path):
        """解压gzip文件"""
        try:
            # 确定解压后的文件名
            extracted_name = gzip_path.name.replace('.gz', '')
            extracted_path = output_dir / extracted_name
            
            logger.info(f"Extracting {gzip_path} to {extracted_path}")
            
            with gzip.open(gzip_path, 'rt', encoding='utf-8') as gz_file:
                with open(extracted_path, 'w', encoding='utf-8') as out_file:
                    # 逐行读取以节省内存
                    for line in gz_file:
                        out_file.write(line)
            
            logger.info(f"Successfully extracted to: {extracted_path}")
            
            # 删除原始gzip文件以节省空间
            gzip_path.unlink()
            logger.info(f"Removed original gzip file: {gzip_path}")
            
        except Exception as e:
            logger.error(f"Error extracting gzip file {gzip_path}: {e}")


class LocalDownloader(BaseDownloader):
    """本地文件下载器（复制器）"""
    
    def download(self, source_path: str, output_dir: str) -> bool:
        """
        从本地路径复制数据集
        
        Args:
            source_path: 源文件或目录路径
            output_dir: 输出目录
            
        Returns:
            是否复制成功
        """
        try:
            import shutil
            
            source = Path(source_path)
            output = Path(output_dir)
            
            if not source.exists():
                logger.error(f"Source path does not exist: {source_path}")
                return False
            
            output.mkdir(parents=True, exist_ok=True)
            
            if source.is_file():
                # 复制单个文件
                dest_file = output / source.name
                shutil.copy2(source, dest_file)
                logger.info(f"Copied file: {source} -> {dest_file}")
            else:
                # 复制整个目录
                dest_dir = output / source.name
                shutil.copytree(source, dest_dir, dirs_exist_ok=True)
                logger.info(f"Copied directory: {source} -> {dest_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error copying from {source_path}: {e}")
            return False


class URLDownloader(BaseDownloader):
    """通用URL下载器"""
    
    def download(self, url: str, output_dir: str) -> bool:
        """
        从URL下载文件
        
        Args:
            url: 文件URL
            output_dir: 输出目录
            
        Returns:
            是否下载成功
        """
        try:
            logger.info(f"Downloading from URL: {url}")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 从URL获取文件名
            parsed_url = urlparse(url)
            filename = Path(parsed_url.path).name
            
            if not filename:
                filename = "downloaded_file"
            
            file_path = output_path / filename
            
            # 下载文件
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"Successfully downloaded: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading from {url}: {e}")
            return False
